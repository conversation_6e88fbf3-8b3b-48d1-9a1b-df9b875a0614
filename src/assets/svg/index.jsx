  // SVG Component for Cake
  const CakeIcon = () => (
    <svg  viewBox="0 0 48 50" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M24 50.0001C37.2548 50.0001 48 45.2991 48 39.5C48 33.701 37.2548 29 24 29C10.7452 29 0 33.701 0 39.5C0 45.2991 10.7452 50.0001 24 50.0001Z" fill="#D0D0D0"/>
      <path d="M4.56055 19V37.7501C4.56055 42.5001 13.2805 46.3334 24.0005 46.3334C34.7205 46.3334 43.4405 42.5001 43.4405 37.8334V19H4.56055Z" fill="#D3976E"/>
      <path d="M43.4396 24.5C43.3596 27.8333 31.1196 31.5 23.9996 31.5C16.8796 31.5 4.63957 27.8333 4.55957 24.5V29.5834C14.5596 37.0834 33.4396 37.0834 43.4396 29.5834V24.5Z" fill="#FFDD7D"/>
      <path d="M43.4396 32.5C43.3596 35.8333 31.1196 39.5 23.9996 39.5C16.8796 39.5 4.63957 35.8333 4.55957 32.5V37.5834C14.5596 45.0834 33.4396 45.0834 43.4396 37.5834V32.5Z" fill="#FFDD7D"/>
      <path d="M4.56055 19V33.0834C4.56055 33.8334 5.20055 34.5001 6.08055 34.5001C6.88055 34.5001 7.60055 33.9167 7.60055 33.0834V29.8334C7.60055 28.8334 8.48055 28 9.60055 28C10.7205 28 11.6005 28.8334 11.6005 29.8334V33.8334C11.6005 35.0001 12.6405 36.0001 13.9205 36.0001C15.2005 36.0001 16.2405 35.0834 16.2405 33.8334V29.75C16.2405 28.6667 17.2005 27.8334 18.3205 27.8334C19.4405 27.8334 20.4005 28.6667 20.4005 29.75V32.3334C20.4005 32.9167 20.8805 33.4167 21.6005 33.4167C22.2405 33.4167 22.8005 32.9167 22.8005 32.3334V30.25C22.8005 29.5 23.4405 28.8334 24.3205 28.8334C25.1205 28.8334 25.8405 29.4167 25.8405 30.25V33.7501C25.8405 34.9167 26.8805 35.9167 28.1605 35.9167C29.4405 35.9167 30.4805 35.0001 30.4805 33.7501V29.8334C30.4805 28.8334 31.3605 28 32.4805 28C33.6005 28 34.4805 28.8334 34.4805 29.8334V32.7501C34.4805 33.3334 35.0405 33.8334 35.6805 33.8334C36.3205 33.8334 36.8805 33.3334 36.8805 32.7501V30.75C36.8805 30.25 37.3605 29.8334 37.9205 29.8334C38.4805 29.8334 38.9605 30.25 38.9605 30.75V33.8334C38.9605 35.0001 40.0005 36.0001 41.2805 36.0001C42.5605 36.0001 43.6005 35.0834 43.6005 33.8334V19.0833L4.56055 19Z" fill="#A80038"/>
      <path d="M24.0005 27.5001C34.737 27.5001 43.4405 23.6945 43.4405 19C43.4405 14.3056 34.737 10.5 24.0005 10.5C13.2641 10.5 4.56055 14.3056 4.56055 19C4.56055 23.6945 13.2641 27.5001 24.0005 27.5001Z" fill="#FF2C68"/>
      <path d="M9.92032 18.1669C9.84032 12.7502 32.8803 12.0002 32.9603 16.8336C33.0403 20.4169 18.3203 20.5836 18.2403 17.7502C18.1603 15.7502 27.3603 15.6669 27.3603 17.1669C27.3603 18.6669 19.9203 18.9169 21.6803 17.0002C18.7203 19.3336 28.1603 19.4169 28.0803 17.2502C28.0003 14.7502 16.9603 15.0836 16.9603 17.6669C17.0403 21.5003 34.0803 21.0836 34.0003 16.7502C33.9203 11.0836 8.40032 11.3336 8.56032 18.0836C8.72032 25.5003 33.5203 28.167 41.6003 18.7503C30.6403 27.417 10.0003 23.2503 9.92032 18.1669Z" fill="#FFA4A4"/>
      <path d="M7.83887 8V17C7.83887 18.25 10.3989 18.0834 10.3989 17V8C10.3989 6.75 7.83887 6.75 7.83887 8Z" fill="#42ADE2"/>
      <path d="M10.3989 8.83301L7.83887 10.2497V11.833L10.3989 10.4997V8.83301ZM7.83887 14.583V16.083L10.3989 14.7497V13.1664L7.83887 14.583Z" fill="#428BC1"/>
      <path d="M10.4789 6.00034C10.4789 8.08368 7.83887 8.08368 7.83887 6.00034C7.83887 5.33367 9.19887 3.16699 9.19887 3.16699C9.19887 3.16699 10.4789 5.33367 10.4789 6.00034Z" fill="#FF8B00"/>
      <path d="M9.83949 6.66667C9.83949 7.83334 8.47949 7.83334 8.47949 6.66667C8.47949 6.33334 9.11949 5.25 9.11949 5.25C9.11949 5.25 9.83949 6.33334 9.83949 6.66667Z" fill="#FFF033"/>
      <path d="M22.8799 4.33301V12.1664C22.8799 13.4164 25.1199 13.1664 25.1199 12.1664V4.33301C25.1199 3.08301 22.8799 3.08301 22.8799 4.33301Z" fill="#42ADE2"/>
      <path d="M25.1199 5L22.8799 6.25V7.66668L25.1199 6.41667V5ZM22.8799 10V11.3334L25.1199 10.1667V8.83335L22.8799 10Z" fill="#428BC1"/>
      <path d="M25.1198 2.50001C25.1198 4.41668 22.7998 4.41668 22.7998 2.50001C22.7998 1.91667 23.9198 0 23.9198 0C23.9198 0 25.1198 1.91667 25.1198 2.50001Z" fill="#FF8B00"/>
      <path d="M24.5595 3.08301C24.5595 4.24968 23.4395 4.24968 23.4395 3.08301C23.4395 2.74968 23.9995 1.83301 23.9995 1.83301C23.9995 1.83301 24.5595 2.74968 24.5595 3.08301Z" fill="#FFF033"/>
      <path d="M37.5205 8V17C37.5205 18.25 40.0805 18.0834 40.0805 17V8C40.0805 6.75 37.5205 6.75 37.5205 8Z" fill="#42ADE2"/>
      <path d="M40.0805 8.83301L37.5205 10.2497V11.833L40.0805 10.4163V8.83301ZM37.5205 14.583V16.083L40.0805 14.6664V13.1664L37.5205 14.583Z" fill="#428BC1"/>
      <path d="M40.1605 6.00034C40.1605 8.08368 37.5205 8.08368 37.5205 6.00034C37.5205 5.33367 38.8805 3.16699 38.8805 3.16699C38.8805 3.16699 40.1605 5.25033 40.1605 6.00034Z" fill="#FF8B00"/>
      <path d="M39.5202 6.58366C39.5202 7.75033 38.1602 7.75033 38.1602 6.58366C38.1602 6.25033 38.8002 5.16699 38.8002 5.16699C38.8002 5.16699 39.5202 6.25033 39.5202 6.58366Z" fill="#FFF033"/>
      <path d="M14.4004 13.5835V24.5002C14.4004 25.9169 17.5204 25.5835 17.5204 24.5002V13.5835C17.5204 12.2502 14.4004 12.2502 14.4004 13.5835Z" fill="#9FE4FF"/>
      <path d="M17.5204 14.6665L14.4004 16.2498V18.2498L17.5204 16.5832V14.6665ZM14.4004 21.4999V23.3332L17.5204 21.7499V19.9165L14.4004 21.4999Z" fill="#42ADE2"/>
      <path d="M17.6004 11.167C17.6004 13.417 14.4004 13.417 14.4004 11.167C14.4004 10.3337 16.0004 7.66699 16.0004 7.66699C16.0004 7.66699 17.6004 10.3337 17.6004 11.167Z" fill="#FF8B00"/>
      <path d="M16.8002 11.917C16.8002 13.167 15.2002 13.167 15.2002 11.917C15.2002 11.5003 16.0002 10.167 16.0002 10.167C16.0002 10.167 16.8002 11.5003 16.8002 11.917Z" fill="#FFF033"/>
      <path d="M30.4004 13.583V24.4997C30.4004 25.9164 33.5204 25.5831 33.5204 24.4997V13.583C33.5204 12.1663 30.4004 12.1663 30.4004 13.583Z" fill="#9FE4FF"/>
      <path d="M33.5204 14.583L30.4004 16.2497V18.1664L33.5204 16.583V14.583ZM30.4004 21.4997V23.333L33.5204 21.6664V19.833L30.4004 21.4997Z" fill="#42ADE2"/>
      <path d="M33.6004 11.083C33.6004 13.333 30.4004 13.333 30.4004 11.083C30.4004 10.2497 32.0004 7.58301 32.0004 7.58301C32.0004 7.58301 33.6004 10.333 33.6004 11.083Z" fill="#FF8B00"/>
      <path d="M32.8002 11.917C32.8002 13.167 31.2002 13.167 31.2002 11.917C31.2002 11.5003 32.0002 10.167 32.0002 10.167C32.0002 10.167 32.8002 11.5003 32.8002 11.917Z" fill="#FFF033"/>
    </svg>
  );
    const SandwichIcon = () => (
    <svg  viewBox="0 0 48 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M46.8298 31.7691C46.4337 32.7567 41.0388 38.233 37.668 41.6685C34.2971 45.104 30.4628 48.1229 30.4628 48.1229L27.5 47.917L28.0229 18.7339L46.3268 25.8062C46.3268 25.8062 46.8734 26.2414 46.9328 27.2712C46.9883 28.3009 47.1625 30.9407 46.8298 31.7691Z" fill="#D46F1E"/>
<path d="M3.78425 33.1548C3.43568 33.225 2.79796 33.2905 2.56426 34.8023C2.33056 36.3141 2.56822 39.3658 3.49906 40.4658C5.48353 42.8107 8.18892 43.5456 10.2922 43.7983C12.1539 44.023 13.2155 43.8077 13.7344 44.1587C14.6375 44.7765 14.6573 46.0169 17.0062 47.6316C18.7807 48.8533 21.8703 50 24.0172 50C27.59 50 30.4539 48.1231 30.4539 48.1231L30.3944 42.2865L28.303 28.8955L3.78425 33.1548Z" fill="#E48216"/>
<path d="M6.16624 38.1678C10.0837 39.9323 14.9399 39.2677 14.9399 39.2677C14.9399 39.2677 16.045 42.1509 19.9387 43.6627C23.8324 45.1745 28.5975 44.4163 31.6792 41.8794C34.7609 39.3426 40.0489 33.5013 41.8472 31.718C43.6455 29.9347 46.1489 27.2528 46.2043 26.5694C46.2598 25.8861 36.1512 16.5391 36.1512 16.5391C36.1512 16.5391 4.34021 33.6604 3.65099 34.1893C3.13605 34.5918 3.72625 37.0678 6.16624 38.1678Z" fill="#FADFB1"/>
<path d="M4.83084 29.8598C4.83084 29.8598 2.79883 29.9956 2.79883 31.6431C2.79883 33.2906 3.55538 34.6667 5.47252 35.6964C7.38966 36.7261 11.517 38.032 14.7691 38.926C18.0646 39.8293 23.0793 43.1853 26.0421 43.527C28.9337 43.864 31.2152 40.9152 32.5501 39.4736C33.885 38.032 40.5118 31.9895 42.0209 30.2015C43.5301 28.4182 46.7266 24.3649 46.7266 24.3649L24.8776 14.957L4.83084 29.8598Z" fill="#EC8B7A"/>
<path d="M6.50256 32.3549C6.50256 32.3549 5.70243 32.3128 5.19146 32.1817C4.68049 32.0506 3.76945 31.62 3.88036 32.0975C3.99127 32.5749 4.84685 33.5765 6.50256 34.4658C8.28899 35.4206 10.6022 35.6032 10.5705 35.5142C10.5705 35.5142 11.743 36.6142 13.0581 37.1103C14.6623 37.7141 16.3378 38.2804 17.6093 38.4115C18.8174 38.5379 19.0353 38.2523 18.5995 37.7328C18.1638 37.2133 16.9953 36.3567 16.9953 36.3567L10.8003 32.0085L6.50256 32.3549ZM20.1681 38.7251C20.1206 39.2118 22.0258 40.4054 23.4122 41.0934C24.7985 41.7815 27.2306 42.6801 27.092 40.6207C26.9969 39.2118 25.4996 39.0574 23.8123 38.8561C22.0377 38.6455 20.2077 38.3272 20.1681 38.7251Z" fill="#D45E51"/>
<path d="M3.98573 21.8468C3.98573 21.8468 2.05275 26.1529 1.39918 25.9797C0.745606 25.8065 0.45249 25.2917 0.159374 25.7223C-0.133742 26.1529 -0.169391 29.4246 1.36353 29.9441C2.89644 30.4636 3.10242 29.8318 4.01345 30.3045C4.92449 30.7772 6.09299 31.5636 6.09299 31.5636C6.09299 31.5636 5.38001 32.2937 5.48696 32.509C5.5939 32.7243 6.75449 33.3983 7.30111 34.0021C7.84773 34.6059 7.95072 35.2705 10.4303 35.7433C12.9099 36.216 13.5001 36.1832 14.4112 36.0569C15.3222 35.9305 15.6866 35.4109 15.6866 35.4109C15.6866 35.4109 16.9977 37.4797 18.3088 37.564C19.6199 37.6482 19.9843 37.1334 20.424 37.1334C20.8637 37.1334 20.2061 37.6482 21.5885 37.6482C22.9709 37.6482 24.5553 37.798 26.3933 38.3878C27.6885 38.8043 28.9125 40.3583 29.7522 40.7046C30.5919 41.051 31.8278 40.7467 32.1922 40.274C32.5566 39.8013 32.5487 38.6639 33.2379 37.8074C33.9271 36.9508 35.7254 35.8603 36.3434 35.0833C36.9613 34.3063 37.3653 31.8772 37.3653 31.8772C37.3653 31.8772 38.205 32.6541 38.9339 32.4809C39.6627 32.3078 40.5024 30.8006 41.156 30.6743C41.8096 30.5479 42.4671 30.3279 43.089 29.5977C43.7109 28.8676 43.4534 26.9298 43.8535 26.5835C44.2535 26.2371 45.7865 27.3136 46.5866 27.0562C47.3867 26.7988 48.1551 25.418 47.9729 24.5147C47.7907 23.6113 47.6085 23.4803 47.6085 22.9654C47.6085 22.4506 48.0838 21.5894 47.4976 20.7281C46.9114 19.8669 36.9256 15.1724 36.9256 15.1724L3.98573 21.8468Z" fill="#B7D019"/>
<path d="M7.30249 28.994C7.30249 28.994 6.82717 30.2437 7.00938 30.5011C7.19158 30.7585 9.88904 33.3843 10.5426 33.4732C11.1962 33.5622 15.9375 33.3422 15.9375 33.3422L13.8976 28.994H7.30249ZM24.43 29.7709C24.43 29.7709 22.3544 35.1535 22.6079 35.6263C22.8614 36.099 24.9053 36.1879 26.6521 36.7449C28.3989 37.3019 28.9099 37.6061 29.4961 37.564C30.0824 37.5219 32.4827 35.9258 32.5224 35.411C32.562 34.8961 30.6646 32.3125 30.6646 32.3125L24.43 29.7709ZM36.4913 28.1328L35.7625 30.3701C35.7625 30.3701 39.1531 30.6275 40.6821 29.7663C42.211 28.905 43.3201 25.7831 43.7954 24.8377C44.453 23.5365 45.3799 22.4132 45.3046 21.9404C45.2293 21.4677 42.9042 19.647 42.9042 19.647L36.4913 28.1328Z" fill="#96A819"/>
<path d="M36.7818 28.6052C36.7818 28.6052 33.102 29.7238 33.0267 29.9813C32.9515 30.2387 29.6004 34.5869 29.6004 34.5869C29.6004 34.5869 32.08 36.0098 34.8488 34.1563C37.6176 32.3122 36.7818 28.6052 36.7818 28.6052ZM45.7812 15.7759C45.7812 15.7759 47.7855 16.4218 47.4568 19.3518C47.0725 22.7779 42.1727 22.6235 42.1727 22.6235C42.1727 22.6235 40.8616 18.4906 41.0795 18.2753C41.2974 18.06 45.7812 15.7759 45.7812 15.7759ZM16.7033 28.9516L13.7524 30.7582C13.7524 30.7582 14.2277 34.4184 19.1473 35.4107C24.0669 36.4029 26.8 31.362 26.8 31.362L16.7033 28.9516ZM7.84648 25.2071L4.6024 25.3803C4.6024 25.3803 4.48357 27.8657 6.17096 29.2979C8.27427 31.0812 11.3441 30.2434 11.3441 30.2434L7.84648 25.2071Z" fill="#DC0D2A"/>
<path d="M13.863 24.4302L7.66797 26.2837C7.66797 26.2837 10.8011 31.1936 12.0409 31.32C13.2807 31.4463 19.4005 29.9439 19.4005 29.9439L13.863 24.4302ZM23.1912 31.8348C23.1912 31.8348 27.0215 35.4903 28.7684 35.3218C30.1151 35.1907 31.8184 32.4011 34.0444 30.9783C36.2705 29.5554 39.6335 28.5116 40.2118 28.0015C41.3565 26.9905 42.9449 21.4581 43.7094 20.1663C44.4738 18.8744 46.3712 15.9023 46.4068 15.4296C46.4425 14.9568 46.078 13.4497 46.078 13.4497L31.6084 27.0139L23.1912 31.8348Z" fill="#FEDF64"/>
<path d="M26.2861 31.1467C26.4683 31.1045 30.0016 31.5773 30.0016 31.5773C30.0016 31.5773 32.8575 28.9655 37.2305 24.7016C41.6034 20.4376 45.6793 15.832 46.5904 13.9598C46.8518 13.4262 46.9667 10.1405 46.9785 9.31671C47.0023 7.88447 46.3289 7.61768 46.3289 7.61768L26.6822 24.2569L26.2861 31.1467Z" fill="#D46F1E"/>
<path d="M30.0425 25.1604C30.0425 25.591 30.2841 31.3012 30.0068 31.5774C29.3493 32.2233 23.5939 34.1189 18.71 31.4931C14.0162 28.9703 13.2041 26.7143 13.2041 26.7143C13.2041 26.7143 7.25468 27.5147 3.72937 25.165C1.47158 23.6579 1.36067 20.7279 1.39632 19.2208C1.43197 17.7136 1.57853 16.2908 2.0895 15.6449C2.60047 14.9989 9.27084 8.41345 10.6929 7.0795C12.1149 5.74555 18.4525 0.231892 19.4388 0.016588C20.4251 -0.198716 28.8779 1.73902 31.718 2.51599C34.558 3.29296 42.8326 5.8298 44.2546 6.47571C45.6766 7.12162 46.552 7.51011 46.5877 7.98284C46.6233 8.45557 46.1876 9.19041 45.6766 9.70527C45.1657 10.2201 34.1976 9.53209 33.9084 11.9004C33.6193 14.2688 30.0425 25.1604 30.0425 25.1604Z" fill="#E38211"/>
<path d="M19.6188 1.11215C18.5137 1.34149 12.283 7.14534 10.5243 8.81629C8.76562 10.4872 4.21836 14.8495 3.44596 15.6592C2.67356 16.4689 1.81402 19.4598 5.58888 21.1822C9.36374 22.9047 14.6398 22.0949 14.6398 22.0949C14.6398 22.0949 16.5292 26.4525 21.0329 27.0142C25.5366 27.5758 28.6262 27.3699 30.5988 25.493C32.5714 23.6161 39.1348 17.0259 40.6361 15.4065C42.1373 13.7823 45.9874 9.73367 45.9121 9.17201C45.6982 7.54787 35.1461 4.81444 30.6424 3.59751C26.5823 2.49758 20.6051 0.910884 19.6188 1.11215Z" fill="#FADFB1"/>
</svg>


  );


    const BurgerIcon = () => (
 <svg viewBox="0 0 48 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.15015 35.912C3.15015 35.912 2.74522 38.5241 3.68583 40.8284C4.47038 42.7583 6.50345 45.2996 10.3924 47.2379C14.2814 49.1762 18.7482 49.8583 25.341 49.9914C31.9337 50.1286 38.7077 48.6105 41.9598 45.7946C45.2161 42.9746 46.3549 40.6038 46.2622 38.0291C46.1694 35.4545 44.7816 34.4771 44.7816 34.4771L3.15015 35.912Z" fill="#FCB745"/>
<path d="M5.03727 33.2871L3.11387 35.1838C3.11387 35.1838 2.59084 37.721 5.49281 40.4661C7.69038 42.5416 11.5794 46.1478 24.4948 46.2434C36.5836 46.335 40.384 43.7146 42.7165 41.8637C45.0533 40.0128 45.9306 36.7976 45.6986 35.895C45.4709 34.9924 44.7538 34.4725 44.7538 34.4725L5.03727 33.2871Z" fill="#E78B20"/>
<path d="M5.85849 35.9953C5.85849 35.9953 5.31437 36.9062 5.49152 37.4843C5.63071 37.9377 5.90488 39.7886 11.3503 42.1344C14.2312 43.3781 19.1325 44.393 25.4974 44.21C33.0476 43.9937 39.0962 42.4963 42.0235 39.7844C45.0225 37.0101 44.4488 35.3589 44.4488 35.3589L5.85849 35.9953Z" fill="#FFD290"/>
<path d="M4.30263 29.6729C4.30263 29.6729 2.49734 31.4863 2.19786 32.2932C1.91525 33.0544 1.95322 34.1608 2.45094 35.055C3.1216 36.2529 3.95676 36.3777 4.99016 36.9891C6.10793 37.6463 7.87526 39.6969 12.224 40.5994C16.5727 41.502 22.2965 41.9096 25.363 41.9096C28.4295 41.9096 32.5041 41.8639 36.3045 40.5994C40.1049 39.335 44.4536 36.8519 45.4617 34.6391C46.4698 32.4263 45.5081 31.1161 45.5081 31.1161L43.3105 29.6729H4.30263Z" fill="#6D544D"/>
<path d="M3.24852 27.3732C3.24852 27.3732 1.84815 27.3109 1.51071 29.403C1.26607 30.9253 2.37961 31.8861 2.37961 31.8861C2.37961 31.8861 1.7891 32.7222 2.47241 33.7828C3.11354 34.7769 3.80107 35.0015 5.21832 35.2261C6.23907 35.3883 6.77475 35.0888 8.10342 35.8125C9.76952 36.7193 10.4824 38.3414 12.7727 38.8364C15.0631 39.3313 17.6276 38.8821 17.7162 38.5203C17.8048 38.1584 14.5105 36.8066 8.55896 33.5083C6.5512 32.3977 4.71216 30.8879 4.21022 30.1226C3.70828 29.3572 3.24852 27.3732 3.24852 27.3732ZM32.0489 38.3872C32.0489 38.9736 34.0187 38.7033 34.934 38.2083C35.8493 37.7134 37.313 36.4489 38.6416 35.904C39.9703 35.3633 40.4722 35.8583 42.3956 34.9557C44.319 34.0531 45.3735 31.9319 45.3735 31.9319C45.3735 31.9319 46.3099 30.7922 46.3858 29.9936C46.5039 28.7749 46.3816 28.1843 45.5127 27.914C44.6438 27.6436 42.7499 30.4512 42.4758 30.7673C42.2016 31.0834 38.1439 34.0074 36.5411 35.1346C34.9382 36.2618 32.0489 38.0711 32.0489 38.3872Z" fill="#9B7169"/>
<path d="M5.03656 25.8378C5.03656 25.8378 2.74619 26.1082 2.42563 27.0565C2.10506 28.0049 3.11316 29.4024 5.76627 31.1659C7.94275 32.6092 15.4001 36.0823 18.3569 37.8916C21.744 39.9671 24.6122 42.8662 25.0888 42.9036C25.6836 42.9494 29.813 38.6236 35.5284 34.822C40.9611 31.2034 44.6856 30.3091 46.0143 27.643C46.7271 26.2122 45.0062 25.3845 45.0062 25.3845L22.9377 17.124L5.03656 25.8378Z" fill="#FFE869"/>
<path d="M25.3637 40.467C26.047 40.3921 29.6196 36.9898 33.8334 34.3736C38.0471 31.7573 44.2729 28.9539 45.1882 27.3776C46.1035 25.8012 43.9059 24.7572 43.9059 24.7572L18.2648 20.5146L3.24879 26.3377C3.24879 26.3377 2.42629 26.6081 2.97462 27.286C3.52296 27.964 4.39187 29.0912 6.31527 30.2641C8.65203 31.6908 14.65 34.0575 18.4462 36.0872C22.2508 38.117 24.5412 40.5543 25.3637 40.467Z" fill="#FFBE01"/>
<path d="M5.31348 27.0112L6.11067 29.1158C6.11067 29.1158 11.8471 34.8183 23.2568 35.0429C34.6116 35.2675 40.9765 29.9394 40.9765 29.9394L42.4275 26.3457L5.31348 27.0112Z" fill="#F4482B"/>
<path d="M20.2712 30.4879L19.3727 31.9062C19.3727 31.9062 21.2919 33.0417 21.8445 33.2372C22.6374 33.5159 23.4178 33.4161 24.5862 33.0584C25.7545 32.7048 30.6052 30.7999 30.6052 30.7999C30.6052 30.7999 34.6165 30.8581 37.1347 30.9038C39.366 30.9454 41.6015 30.1552 42.7699 28.8242C43.9383 27.4932 45.752 24.6399 45.752 24.6399C45.752 24.6399 46.6968 24.4194 47.3253 23.7082C47.9538 22.9969 48.0002 22.3772 48.0002 22.3772C48.0002 22.3772 47.8188 21.1793 47.1481 20.8715C46.4733 20.5596 16.4159 18.7461 16.4159 18.7461L4.37358 19.632L2.34473 23.7539L9.03867 29.3815L18.4743 29.9139L20.2712 30.4879Z" fill="#A6B732"/>
<path d="M2.97369 20.1235C2.2946 19.8198 1.58176 19.8573 1.26541 20.7432C0.949059 21.6292 1.04607 22.0201 1.04607 22.0201C1.04607 22.0201 0.122334 22.2115 0.00844895 23.3553C-0.126526 24.7279 1.40038 25.2145 1.40038 25.2145C1.40038 25.2145 1.47209 25.7469 2.07526 26.2793C2.76279 26.8866 3.45032 26.8242 3.45032 26.8242C3.45032 26.8242 5.26405 28.5836 6.47883 29.2906C7.69361 29.9977 9.44408 30.4885 11.1988 30.4428C12.9534 30.397 13.5355 29.6026 14.8389 30.0435C16.1422 30.4844 19.5968 32.2771 20.9887 32.3062C23.1905 32.3519 27.9526 28.9829 29.0324 29.0286C30.1122 29.0744 35.0093 29.8272 37.616 29.8272C40.2227 29.8272 41.1675 29.0744 41.7496 28.3215C42.3317 27.5687 43.3229 25.6637 43.0066 25.618C42.6902 25.5722 40.6234 27.2567 39.1429 27.1236C37.6582 26.9905 36.359 26.1504 34.5158 26.1504C32.6725 26.1504 23.6882 27.3025 23.1019 27.5687C22.5156 27.8349 20.0017 29.7399 19.5082 29.6525C19.0147 29.5652 17.3064 28.1468 16.7243 27.8349C16.1422 27.5229 10.0135 27.3233 9.13194 27.4813C8.49925 27.5936 7.60503 27.7475 7.60503 27.7475C7.60503 27.7475 7.69361 26.375 6.93016 25.5764C6.1667 24.7778 3.64857 23.4052 3.64857 23.4052C3.64857 23.4052 3.9607 20.5644 2.97369 20.1235ZM42.8252 22.5151C42.8252 22.5151 45.5669 22.5151 46.3303 22.4693C47.0938 22.4236 47.9922 22.382 47.9922 22.382C47.9922 22.382 47.7307 21.155 47.3342 20.502C46.5244 19.1627 44.5757 18.7925 44.5757 18.7925L42.8252 22.5151Z" fill="#C2DD1F"/>
<path d="M2.34538 18.4846L2.0459 19.7906C2.0459 19.7906 6.19218 27.032 24.8272 27.3066C35.7349 27.4688 42.0029 24.2037 44.7699 21.5334C46.4275 19.9321 46.4823 18.3765 46.4823 18.3765L2.34538 18.4846Z" fill="#DE8010"/>
<path d="M1.40008 14.8825C1.16387 18.5011 2.05809 19.7988 2.05809 19.7988C2.05809 19.7988 8.22478 25.3058 23.9747 25.5595C40.1169 25.8174 45.3641 20.5392 46.2836 19.0002C46.8699 18.0228 47.3381 16.1053 46.967 13.3103C46.3511 8.69339 40.9605 -0.0661724 23.8988 0.000377038C6.83706 0.0669264 1.74595 9.62924 1.40008 14.8825Z" fill="url(#paint0_radial_134_1471)"/>
<path d="M17.61 4.50487C17.7408 5.21196 14.5056 7.56614 14.0374 5.30762C13.5987 3.1822 17.4877 3.85186 17.61 4.50487ZM18.7658 8.93457C18.9345 9.3713 22.2498 9.99936 21.8955 8.15677C21.5243 6.23932 18.4199 8.03615 18.7658 8.93457ZM21.672 3.87681C21.7563 4.50487 25.62 5.23692 25.5399 3.46504C25.4428 1.27723 21.575 3.14893 21.672 3.87681ZM15.2986 10.3903C15.3197 10.5318 14.3706 11.268 13.4005 11.5549C12.5485 11.8087 11.6753 11.7005 11.5783 10.6565C11.4855 9.64997 12.211 9.28395 13.0799 9.3713C14.0796 9.46696 15.2564 10.12 15.2986 10.3903ZM23.3254 13.331C23.1651 12.6697 19.5334 11.7504 19.7781 13.8426C20.027 15.9264 23.4984 14.0339 23.3254 13.331ZM26.7251 8.56854C26.548 8.36474 25.717 8.40217 24.7047 8.66421C23.975 8.85554 23.4351 9.66661 23.9412 10.4361C24.5318 11.3345 25.523 10.956 26.1557 10.1948C26.6703 9.57926 26.9234 8.79731 26.7251 8.56854ZM30.6141 3.70628C30.5002 3.58566 29.6693 3.50247 28.9902 3.8269C28.3069 4.15133 27.7163 4.92497 28.349 5.64869C28.9902 6.37657 29.9434 5.95648 30.3948 5.187C30.7533 4.56726 30.787 3.88929 30.6141 3.70628ZM33.2208 10.0368C33.1111 9.35882 30.0573 8.15261 30.0784 9.99936C30.0995 11.8919 33.3178 10.6482 33.2208 10.0368ZM37.443 6.69268C37.3418 6.17277 33.457 5.75267 34.1192 7.68677C34.8068 9.70404 37.5653 7.3249 37.443 6.69268ZM32.0946 13.4974C32.1199 12.9151 29.1631 11.5799 28.9438 13.3268C28.7244 15.0737 32.0609 14.2544 32.0946 13.4974Z" fill="#FDEAC8"/>
<defs>
<radialGradient id="paint0_radial_134_1471" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(24.0526 4.98865) rotate(89.3492) scale(18.3179 32.3684)">
<stop offset="0.517" stopColor="#DF8016"/>
<stop offset="0.643" stopColor="#E58C21"/>
<stop offset="1" stopColor="#F5AC3C"/>
</radialGradient>
</defs>
</svg>

  );


  
    const PizzaIcon = () => (
  <svg  viewBox="0 0 50 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.4167 48C32.75 47.92 49.9167 31.36 50 11.84L2.98023e-06 7.06816e-09L12.4167 48Z" fill="#F6DA77"/>
<path d="M10.8338 41.7614L11.1672 43.4414C27.0005 42.6414 43.5838 31.7614 45.2505 10.6414L43.5838 10.4014C41.4172 26.5614 27.7505 39.7614 10.8338 41.7614Z" fill="#860D16"/>
<path d="M11.1667 43.3601L12.3333 47.9201C32.6667 48.0001 50 31.3601 50 11.6801L45.25 10.5601C43.4167 29.3601 30.5833 41.6001 11.1667 43.3601Z" fill="#C98E52"/>
<path d="M16.917 8.96062C16.917 7.60062 17.417 6.40062 18.417 5.44062C18.8337 5.04063 19.5003 5.04063 19.8337 5.44062C20.2503 5.84062 20.2503 6.48062 19.8337 6.80062C18.667 7.92062 18.667 9.92062 19.8337 11.0406C20.417 11.6006 21.167 11.9206 22.0003 11.9206C22.8337 11.9206 23.5837 11.6006 24.167 11.0406C24.5837 10.6406 25.2503 10.6406 25.5837 11.0406C26.0003 11.4406 26.0003 12.0806 25.5837 12.4006C24.5837 13.3606 23.3337 13.8406 21.917 13.8406C20.5003 13.8406 19.2503 13.3606 18.2503 12.4006C17.5003 11.5206 16.917 10.2406 16.917 8.96062ZM33.417 29.0406H33.0003C32.7503 28.9606 32.5837 28.8806 32.417 28.6406C32.2503 28.4006 32.2503 28.1606 32.3337 28.0006C32.5003 27.3606 32.3337 26.7206 32.0003 26.1606C31.667 25.6006 31.0837 25.2006 30.417 25.0406C29.0837 24.7206 27.667 25.6006 27.3337 26.8806C27.2503 27.3606 26.667 27.6806 26.2503 27.5206C25.7503 27.4406 25.417 26.9606 25.5837 26.4806C26.0003 24.8806 27.2503 23.6806 28.917 23.2806C29.5837 23.1206 30.2503 23.1206 30.8337 23.2806C32.0003 23.5206 32.917 24.2406 33.5837 25.2006C34.2503 26.1606 34.417 27.2806 34.167 28.4006C34.0003 28.6406 33.7503 28.9606 33.417 29.0406ZM9.25033 33.2806V32.8806C9.33367 32.6406 9.417 32.4806 9.667 32.4006C9.83367 32.3206 10.0837 32.2406 10.3337 32.3206C10.917 32.4806 11.5837 32.4006 12.167 32.0806C12.7503 31.7606 13.167 31.2806 13.3337 30.6406C13.667 29.4406 12.8337 28.0806 11.667 27.7606C11.2503 27.6806 10.917 27.1206 11.0837 26.7206C11.2503 26.2406 11.667 26.0006 12.167 26.1606C13.667 26.5606 14.917 27.7606 15.167 29.2806C15.2503 29.8406 15.2503 30.4806 15.0837 31.0406C14.8337 32.0806 14.0837 32.9606 13.167 33.4406C12.167 34.0006 11.0837 34.0806 10.0003 33.8406C9.58367 33.8406 9.33367 33.6006 9.25033 33.2806Z" fill="#83BF4F"/>
<path d="M21.4995 27.8392C18.0829 28.9592 14.4162 27.1992 13.2495 23.8392C12.0829 20.4792 13.9162 16.8792 17.3329 15.8392C20.7495 14.7192 24.4162 16.4792 25.5829 19.8392C26.7495 23.1192 24.9162 26.7192 21.4995 27.8392ZM20.8329 37.8392C18.7495 38.4792 16.5829 37.4392 15.8329 35.4392C15.1662 33.4392 16.2495 31.2792 18.3329 30.6392C20.4162 29.9992 22.5829 31.0392 23.3329 33.0392C23.9995 34.9592 22.9162 37.1192 20.8329 37.8392ZM35.8329 21.3592C33.0829 22.2392 29.9995 20.7992 29.0829 18.0792C28.1662 15.3592 29.6662 12.3992 32.4162 11.5192C35.1662 10.6392 38.2495 12.0792 39.1662 14.7992C40.0829 17.5192 38.5829 20.4792 35.8329 21.3592ZM6.66621 25.6792L4.66621 17.9192C6.83288 17.1992 9.41621 18.6392 9.99955 20.7192C10.6662 23.2792 8.74955 24.9592 6.66621 25.6792ZM12.4995 13.8392C9.66621 14.7992 6.49955 13.2792 5.58288 10.4792C4.58288 7.6792 6.16622 4.6392 9.08288 3.6792C11.9162 2.7192 15.0829 4.2392 15.9995 7.0392C16.9162 9.8392 15.3329 12.8792 12.4995 13.8392Z" fill="#B21725"/>
<path d="M45.25 10.5596C45.1667 14.7996 44.3333 19.0396 42.6667 23.0396C41.0833 27.0396 38.5833 30.7996 35.3333 33.8396C32.1667 36.8796 28.25 39.2796 24.0833 40.8796C19.9167 42.4796 15.5 43.2796 11.0833 43.3596C15.4167 42.6396 19.6667 41.5196 23.5833 39.7596C27.5 37.9996 31.1667 35.6796 34.1667 32.7196C37.25 29.8396 39.6667 26.3196 41.4167 22.5596C43.3333 18.7996 44.5 14.7196 45.25 10.5596Z" fill="#E0A763"/>
<path d="M40.4701 11.5952L42.1201 13.1792L40.4701 14.7632L38.8201 13.1792L40.4701 11.5952ZM35.7968 21.936L37.4468 23.52L35.7968 25.104L34.1468 23.52L35.7968 21.936ZM29.6268 8.48637L31.2768 10.0704L29.6268 11.6544L27.9768 10.0704L29.6268 8.48637ZM24.8876 13.9584L26.5376 15.5424L24.8876 17.1264L23.2376 15.5424L24.8876 13.9584ZM24.6751 28.196L26.3251 29.78L24.6751 31.364L23.0251 29.78L24.6751 28.196ZM12.8826 37.3792L14.5326 38.9632L12.8826 40.5472L11.2326 38.9632L12.8826 37.3792ZM14.4568 13.8536L16.1068 15.4376L14.4568 17.0216L12.8068 15.4376L14.4568 13.8536ZM8.41429 14.9872L10.0643 16.5712L8.41429 18.1552L6.76429 16.5712L8.41429 14.9872ZM3.13262 1.63037L4.78262 3.21437L3.13262 4.79837L1.48262 3.21437L3.13262 1.63037ZM37.2268 9.33597L38.4643 10.524L37.2268 11.712L35.9893 10.524L37.2268 9.33597ZM28.4226 19.4016L29.6601 20.5896L28.4226 21.7776L27.1851 20.5896L28.4226 19.4016ZM28.371 30.22L29.6085 31.408L28.371 32.596L27.1335 31.408L28.371 30.22ZM21.5301 8.90877L22.7676 10.0968L21.5301 11.2848L20.2926 10.0968L21.5301 8.90877ZM16.5835 4.04797L17.821 5.23597L16.5843 6.42397L15.3468 5.23597L16.5835 4.04797ZM6.26262 2.18397L7.50012 3.37197L6.26262 4.55997L5.02512 3.37197L6.26262 2.18397ZM5.59513 12.4608L6.83262 13.6488L5.59429 14.8368L4.35762 13.6488L5.59513 12.4608ZM11.3785 22.7824L12.616 23.9704L11.3785 25.1584L10.141 23.9704L11.3785 22.7824ZM9.25346 27.7936L10.491 28.9816L9.25346 30.1696L8.01679 28.9816L9.25346 27.7936ZM14.0251 34.4608L15.2618 35.6488L14.0243 36.8368L12.7868 35.6488L14.0251 34.4608Z" fill="#FFAB41"/>
</svg>

  );

// Export all SVG components
export { CakeIcon, SandwichIcon, BurgerIcon, PizzaIcon };