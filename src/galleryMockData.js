// Mock data for Gallery component
// No dynamic data required - all content is static text and images from the design
export const galleryContent = {
  header: "GALLERY",
  brand: "FeelFood",
  freshFastDelicious: {
    fresh: "Fresh,",
    fast: "Fast",
    delicious: "Delicious"
  },
  quote: "Life is a combination of magic & pasta",
  discount: {
    title: "Discount",
    percentage: "50%",
    period: "Weekend"
  },
  description: "Food for us comes from our relatives, whether they have wings or fins or roots. That is how we consider food. Food has a culture. It has a history. It has a story. It has relationships."
};

// Image paths for gallery items
export const galleryImages = {
  header: "/gallery/imageGallery/gallery-header.jpg",
  cake: "/gallery/cake/end-cake.png",
  pizza: "/gallery/pizza/pizza-main.svg",
  juice: "/gallery/juice/juice-glass.svg",
  pasta: "/gallery/pasta/pasta-dish.svg",
  background: "/gallery/background-texture.svg"
};

// ... existing code ...