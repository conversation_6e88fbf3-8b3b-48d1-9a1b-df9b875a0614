import React, { useState } from 'react';
import ShopInfo from './ShopInfo';
import Branches from './Branches';
import LocationMap from './LocationMap';

const HowToFindUs = () => {
  const [selectedBranch, setSelectedBranch] = useState(null);

  const handleBranchSelect = (branch) => {
    setSelectedBranch(branch);
  };

  return (
    <section className="bg-gray-50 py-8 md:py-12 px-4 sm:px-6 lg:px-8 w-full lg:w-4/5 xl:w-4/6 mx-auto text-center">
      {/* Judul */}
      <h2 className="text-center text-xl sm:text-2xl font-bold mb-6 md:mb-8">HOW TO FIND US</h2>

      {/* Info dan cabang */}
      <div className="flex flex-col lg:flex-row gap-8 lg:gap-10 xl:gap-40 items-start justify-center">
        {/* Kiri: Info toko */}
        <ShopInfo />

        {/* Kanan: Cabang */}
        <Branches onBranchSelect={handleBranchSelect} />
      </div>

      {/* Map */}
      <LocationMap mapUrl={selectedBranch?.mapUrl} />
    </section>
  );
};

export default HowToFindUs;
