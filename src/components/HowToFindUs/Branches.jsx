import React, { useState } from 'react';
import { branches } from '../../data/contact';

const Branches = ({ onBranchSelect }) => {
  const [selectedBranch, setSelectedBranch] = useState(branches[0]);

  const handleBranchChange = (e) => {
    const branchId = parseInt(e.target.value);
    const branch = branches.find(b => b.id === branchId);
    setSelectedBranch(branch);
    if (onBranchSelect) {
      onBranchSelect(branch);
    }
  };

  return (
    <div className="w-full lg:w-auto">
      <h3 className="text-base sm:text-lg font-semibold mb-2">Our Branches</h3>
      <div className="bg-white rounded-2xl p-4 sm:p-6 w-full max-w-[300px] mx-auto lg:mx-0">
        <select 
          className="bg-gray-100 py-2 sm:py-[10px] px-4 sm:px-5 block w-full border-0 rounded-full mb-3 sm:mb-4 shadow-[0_5px_6px_rgba(0,0,0,0.15)] text-sm sm:text-base text-center"
          value={selectedBranch.id}
          onChange={handleBranchChange}
        >
          {branches.map((branch) => (
            <option key={branch.id} value={branch.id}>
              {branch.name}
            </option>
          ))}
        </select>
        {selectedBranch.address.map((line, index) => (
          <p key={index} className="mb-1 sm:mb-2 text-sm sm:text-base">
            {line}
          </p>
        ))}
        <p className="text-sm sm:text-base">Contact: {selectedBranch.phone}</p>
      </div>
    </div>
  );
};

export default Branches;
