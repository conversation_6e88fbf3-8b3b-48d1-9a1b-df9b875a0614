import React from 'react';
import { shopInfo } from '../../data/contact';

const ShopInfo = () => {
  return (
    <div className="w-full lg:w-auto">
      <h3 className="text-base sm:text-lg font-semibold mb-2">Shop timings</h3>
      <p className="mb-4 sm:mb-6">{shopInfo.timings}</p>

      <h3 className="text-base sm:text-lg font-semibold mb-2">Production Unit</h3>
      <p className="text-sm sm:text-base">
        {shopInfo.productionUnit.address.map((line, index) => (
          <React.Fragment key={index}>
            {line}
            {index < shopInfo.productionUnit.address.length - 1 && <br className="hidden sm:block" />}
          </React.Fragment>
        ))}
      </p>
    </div>
  );
};

export default ShopInfo;
