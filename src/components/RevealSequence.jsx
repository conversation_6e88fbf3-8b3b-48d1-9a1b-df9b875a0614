import React, { useState, useEffect, useRef } from 'react';
import MenuSection from './Menu';
import AboutUs from './AboutUs';
import Gallery from './Gallery';

const RevealSequence = () => {
  const containerRef = useRef(null);
  const aboutUsRef = useRef(null);
  const sliderRef = useRef(null);
  const galleryRef = useRef(null);
  const [progress, setProgress] = useState(0);
  const [contentHeight, setContentHeight] = useState(0);
  const [componentHeights, setComponentHeights] = useState({
    slider: 0,
    aboutUs: 0,
    gallery: 0
  });
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [viewportHeight, setViewportHeight] = useState(0);

  const getChildElement = (ref) => {
    return ref.current?.firstElementChild?.firstElementChild ||
           ref.current?.firstElementChild ||
           ref.current;
  };
  
  useEffect(() => {
    const measureComponents = () => {
      setTimeout(() => {
        const vh = window.innerHeight;
        const mobile = window.innerWidth < 769;
        
        const heights = {
          slider: sliderRef.current?.firstElementChild?.scrollHeight || 0,
          aboutUs: aboutUsRef.current?.firstElementChild?.scrollHeight || 0,
          // Perbaiki tinggi gallery untuk mobile
          gallery: Math.max(
            galleryRef.current?.firstElementChild?.scrollHeight || 0,
            mobile ? vh * 1.5 : 0
          )
        };
        setComponentHeights(heights);
        const totalHeight = heights.slider + heights.aboutUs + heights.gallery;
        setContentHeight(totalHeight);
      }, 200);
    };

    measureComponents();
    window.addEventListener('resize', measureComponents);
    window.addEventListener('orientationchange', () => {
      setTimeout(measureComponents, 100);
    });
    
    return () => {
      window.removeEventListener('resize', measureComponents);
      window.removeEventListener('orientationchange', measureComponents);
    };
  }, []);

  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 769;
      setIsMobile(mobile);
      const tablet = window.innerWidth < 1025;
      setIsTablet(tablet);
      setViewportHeight(window.innerHeight);

      setTimeout(() => {
        const vh = window.innerHeight;
        const heights = {
          slider: sliderRef.current?.firstElementChild?.scrollHeight || 0,
          aboutUs: aboutUsRef.current?.firstElementChild?.scrollHeight || 0,
          // Perbaiki tinggi gallery untuk mobile
          gallery: Math.max(
            galleryRef.current?.firstElementChild?.scrollHeight || 0,
            mobile ? vh * 1.5 : 0
          )
        };

        setComponentHeights(heights);
        setContentHeight(heights.slider + heights.aboutUs + heights.gallery);
      }, 100);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    window.addEventListener('orientationchange', () => {
      setTimeout(checkMobile, 100);
    });
    
    return () => {
      window.removeEventListener('resize', checkMobile);
      window.removeEventListener('orientationchange', checkMobile);
    };
  }, []);

  const throttle = (func, delay) => {
    let timeoutId;
    let lastExecTime = 0;
    return function (...args) {
      const currentTime = Date.now();
      if (currentTime - lastExecTime > delay) {
        func.apply(this, args);
        lastExecTime = currentTime;
      } else {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          func.apply(this, args);
          lastExecTime = Date.now();
        }, delay - (currentTime - lastExecTime));
      }
    };
  };

  useEffect(() => {
    const handleScroll = () => {
      const element = containerRef.current;
      if (!element) return;

      const { top, height } = element.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const totalScrollDistance = height - viewportHeight;
      const currentScroll = -top;
      
      if (totalScrollDistance <= 0) {
        setProgress(0);
        return;
      }

      const currentProgress = currentScroll / totalScrollDistance;
      setProgress(Math.max(0, Math.min(1, currentProgress)));
    };

    const throttledHandleScroll = throttle(handleScroll, 16);
    handleScroll();
    window.addEventListener('scroll', throttledHandleScroll, { passive: true });
    
    return () => window.removeEventListener('scroll', throttledHandleScroll);
  }, [contentHeight]);

  const easeInOutCubic = t => t < 0.5 
    ? 4 * t * t * t 
    : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;

  const easedProgress = easeInOutCubic(progress);
  

// total tinggi asli
// 1. Tinggi asli
const totalHeightAsli =
  componentHeights.slider +
  componentHeights.aboutUs +
  componentHeights.gallery;

// 2. Tambahan jarak scroll biar pelan (dikurangi untuk scroll lebih cepat)
const extraScrollSpace = totalHeightAsli * (isMobile ? 0.15 : 0.2);

// 3. Total tinggi scroll
const totalScrollHeight = totalHeightAsli + extraScrollSpace;

// 4. Hitung fase (dikembalikan ke normal)
const sliderPhase = (componentHeights.slider / totalScrollHeight) * 0.6;
const aboutUsPhase = ((componentHeights.slider + componentHeights.aboutUs) / totalScrollHeight) * 0.85;

// 5. Translate
const sliderTranslateY =
  progress <= sliderPhase
    ? 0
    : -((progress - sliderPhase) / (aboutUsPhase - sliderPhase)) *
      Math.min((componentHeights.slider / viewportHeight) * 100, 100);

const aboutUsTranslateY = 0;

const galleryTranslateY =
  progress <= aboutUsPhase
    ? Math.min((componentHeights.gallery / viewportHeight) * 100, 100)
    : Math.min((componentHeights.gallery / viewportHeight) * 100, 100) -
      (((progress - aboutUsPhase) / (1 - aboutUsPhase)) *
        Math.min((componentHeights.gallery / viewportHeight) * 100, 100));
  return (
<div
  ref={containerRef}
  style={{
    height: `${totalScrollHeight}px`
  }}
>
      <div 
        className="sticky top-0 w-full overflow-hidden"
        style={{ height: `${viewportHeight}px` }}
      >
        <div 
          className="absolute inset-0 z-10 flex items-center justify-center bg-white"
          style={{
            transform: `translateY(${aboutUsTranslateY}%)`,
            height: `${componentHeights.aboutUs}px`
          }}
        >
          <div ref={aboutUsRef} className="w-full h-full">
            <AboutUs />
          </div>
        </div>

        <div 
          className="absolute inset-0 z-20 bg-gray-50"
          style={{
            transform: `translateY(${sliderTranslateY}%)`,
            height: `${componentHeights.slider}px`
          }}
        >
          <div ref={sliderRef}>
            <MenuSection />
          </div>
        </div>

        <div 
          className="absolute inset-0 z-30 bg-white"
          style={{
            transform: `translateY(${galleryTranslateY}%)`,
            height: `${viewportHeight}px`
          }}
        >
          <div 
            ref={galleryRef} 
            className="w-full h-full"
            style={{
              overflowY: (isMobile || isTablet) && progress > aboutUsPhase && galleryTranslateY <= 10 ? 'auto' : 'hidden'
            }}
          >
            <Gallery />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RevealSequence;