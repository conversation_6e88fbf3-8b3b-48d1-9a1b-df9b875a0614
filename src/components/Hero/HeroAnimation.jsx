import { useLayoutEffect } from 'react';
import gsap from 'gsap';

const useHeroAnimation = (root) => {
  useLayoutEffect(() => {
    if (!root.current) return;
    
    const ctx = gsap.context(() => {
      // The timeline will be created here
      const tl = gsap.timeline();

      // 1. Leaf animation
      tl.from('.leaf', {
        scale: 0.5,
        opacity: 0,
        duration: 0.8,
        ease: 'back.out(1.7)',
      });

      // 2. <PERSON> Hero and <PERSON>uce animation
      tl.from(
        '.main-hero',
        {
          scale: 0.2,
          rotate: -45,
          opacity: 0,
          duration: 1,
          transformOrigin: 'center center',
          ease: 'elastic.out(1, 0.75)',
        },
        '-=0.5'
      );

      tl.from(
        '.sauce',
        {
          scale: 0,
          opacity: 0,
          duration: 0.6,
          ease: 'back.out(1.7)',
        },
        '-=0.6'
      );

      // 3. Background Hero animation
      tl.from(
        '.bg-hero',
        {
          opacity: 0,
          duration: 1,
        },
        '<'
      );

      // 4. Food items animation
      tl.from(
        '.grilled-chicken',
        {
          x: -100,
          opacity: 0,
          duration: 0.8,
          ease: 'back.out(1.7)',
        },
        '-=0.2'
      );

      tl.from(
        '.fish-veg-1',
        {
          y: 100,
          opacity: 0,
          duration: 0.8,
          ease: 'back.out(1.7)',
        },
        '<0.2'
      );

      tl.from(
        '.fish-veg-2',
        {
          x: 100,
          opacity: 0,
          duration: 0.8,
          ease: 'back.out(1.7)',
        },
        '<0.2'
      );

      // 5. Italian Pizza and Comments animation
      tl.from(
        ['.italian-pizza', '.comment1', '.comment2'],
        {
          y: 10,
          opacity: 0,
          duration: 0.7,
          ease: 'back.out(1.7)',
          stagger: 0.2,
        },
        '-=0.5'
      );

      // 6. Yoyo animation for Italian Pizza and Comments
      gsap.to(['.italian-pizza', '.comment1', '.comment2'], {
        y: '0px',
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        stagger: {
          each: 0.1,
          from: 'random',
        },
      });
    }, root);

    // Cleanup function
    return () => ctx.revert();
  }, [root]);
};

export default useHeroAnimation;
