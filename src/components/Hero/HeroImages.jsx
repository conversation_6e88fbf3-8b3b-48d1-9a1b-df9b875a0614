import React from 'react';

const HeroImages = () => {
  return (
    <div className="w-[100%] md:w-[50%] relative hero-item pb-[100%] md:pb-[50%] md:h-0">
      {/* Main Images */}
      <img
        className="leaf absolute z-0 rounded-lg top-0 right-[10%] brightness-110 w-[50%] h-auto"
        src="/hero/bg2-main-hero.webp"
        alt="leaf"
      />       
      <img
        className="bg-hero absolute z-1 rounded-lg opacity-50 w-[100%] h-auto top-[10px] left-[0%]"
        src="/hero/bg-main-hero.webp"
        alt="Background Hero"
      />
      <img
        className="main-hero absolute z-10 rounded-lg w-[80%] h-auto top-[12%] left-[15%]"
        src="/hero/main-hero.webp"
        alt="Main Hero"
      />
      
      {/* Food Items */}
      <img
        className="grilled-chicken absolute z-10 w-[20%] h-auto -left-[4%] top-[61%]"
        src="/hero/grilled-chicken.webp"
        alt="grilled-chicken"
      />
      <img
        className="fish-veg-1 absolute z-10 w-[20%] h-auto left-[40%] top-[90%]"
        src="/hero/fish-vegetables.webp"
        alt="fish and vegetables"
      />
      <img
        className="fish-veg-2 absolute z-10 w-[20%] h-auto left-[83%] top-[67%]"
        src="/hero/fish-vegetables2.webp"
        alt="fish and vegetables 2"
      />
      <img
        className="sauce absolute z-10 w-[15%] h-auto left-[35%] top-[76%]"
        src="/hero/sauce.webp"
        alt="sauce"
      />
      <img
        className="italian-pizza absolute z-10 w-[50%] md:w-[30%] h-auto left-[-7%] md:left-[0%] top-[30%] md:top-[40%]"
        src="/hero/italian-pizza.webp"
        alt="italian pizza"
      />
      
      {/* Comments */}
      <img
        className="comment2 absolute z-10 w-[43%] md:w-[25%] h-auto left-[60%] md:left-[65%] top-[30%] md:top-[37%]"
        src="/hero/comment2.webp"
        alt="comment2"
      />
      <img
        className="comment1 absolute z-10 w-[43%] md:w-[25%] h-auto left-[4%] md:left-[18%] top-[70%] md:top-[75%]"
        src="/hero/comment1.webp"
        alt="comment1"
      />
      

    </div>
  );
};

export default HeroImages;
