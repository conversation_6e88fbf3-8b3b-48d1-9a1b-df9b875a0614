import React, { useRef } from 'react';
import HeroText from './HeroText';
import HeroButtons from './HeroButtons';
import HeroImages from './HeroImages';
import useHeroAnimation from './HeroAnimation';

const HeroSection = () => {
  const root = useRef(null);
  
  // Initialize animations
  useHeroAnimation(root);

  return (
    <section 
      ref={root} 
      className=" w-[100vw] bg-gray-50"
    >
 <div className='relative w-[100vw] max-w-[1500px] mx-auto flex flex-col-reverse md:flex-row justify-around gap-8 px-8 xl:py-16 md:py-8 bg-gray-50'>     
  <div className="hero-item w-[100%] md:w-[50%] flex-1 pt-15 z-11 pb-[50%] h-0 -mt-[15%] md:-mt-0">
        <HeroText />
        <HeroButtons />
        {/* Decorative Elements */}
        <img 
          className="z-0 absolute -bottom-[100px] left-[-15%] w-[25%] drop-shadow-[0_20px_20px_rgba(88,195,18,0.74)]"
          src="/hero/big-leaf.webp"
          alt="big-leaf"
        />
        <img
          className="z-0 absolute top-[33%] sm:top-[26%] md:top-[29%] left-[3%] sm:left-[1%] md:-left-[1%] xl2:-left-[3%] w-[6%] drop-shadow-[0_20px_20px_rgba(88,195,18,0.74)]"
          src="/hero/leaf2.webp"
          alt="leaf2"
        />
        <img
          className="z-0 absolute top-[90%] left-[6%] w-[5%] drop-shadow-[0_20px_20px_rgba(88,195,18,0.74)]"
          src="/hero/leaf3.webp"
          alt="leaf3"
        />
    </div>

      <HeroImages /></div>
      
    </section>
  );
};

export default HeroSection;
