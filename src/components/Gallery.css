.my-masonry-grid {
  display: -webkit-box; /* Not needed if you use latest versions */
  display: -ms-flexbox;
  display: flex;
  margin-left: -1rem; /* gutter size offset */
  width: auto;
}
.my-masonry-grid_column {
  padding-left: 1rem; /* gutter size */
  background-clip: padding-box;
}
.my-masonry-grid_column > div {
  margin-bottom: 1rem;
}

/* Base Gallery Styles */
.gallery-item {
  will-change: transform, opacity; /* Optimize for animations */
  transform: translateZ(0); /* Force hardware acceleration */
  backface-visibility: hidden; /* Improve rendering performance */
  perspective: 1000px; /* Enable 3D effects for children */
}

/* Card Hover Effects */
.gallery-item > div {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.gallery-item:hover > div {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 
              0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Image Containers */
.gallery-item img {
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  backface-visibility: hidden;
}

.gallery-item:hover img {
  transform: scale(1.03);
}

/* Overlay Effects */
.gallery-overlay {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );
  transition: opacity 0.3s ease;
}

/* Buttons in Gallery */
.gallery-button {
  @apply bg-amber-500 text-white px-6 py-2 rounded-full font-medium 
         transition-all duration-300 hover:bg-amber-600 hover:shadow-lg
         focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50;
}

/* Badges */
.gallery-badge {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold
         bg-white text-amber-700 shadow-md transition-all duration-300
         hover:bg-amber-50;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .gallery-item {
    margin-bottom: 1.5rem;
  }
  
  .gallery-item:last-child {
    margin-bottom: 0;
  }
}

/* Animation Performance */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Scrollbar Styling (WebKit) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Print Styles */
@media print {
  .gallery-item {
    break-inside: avoid;
    page-break-inside: avoid;
  }
  
  .gallery-item > div {
    box-shadow: none !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .gallery-item {
    --tw-bg-opacity: 1;
    background-color: rgba(31, 41, 55, var(--tw-bg-opacity));
  }
  
  .gallery-item h3,
  .gallery-item p {
    color: #f3f4f6;
  }
}