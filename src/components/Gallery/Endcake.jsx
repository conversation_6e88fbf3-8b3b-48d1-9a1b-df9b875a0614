// components/Gallery/EndcakeWithHover.jsx
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

const Endcake = ({ endcake, text }) => {
  const overlayRef = useRef(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Set initial state
      gsap.set(overlayRef.current, { opacity: 0 });
      
      // Create timeline with smooth loop
      const tl = gsap.timeline({ 
        repeat: -1,
        repeatDelay: 4,
        defaults: { ease: 'power1.inOut' }
      });
      
      // Fade in
      tl.to(overlayRef.current, {
        opacity: 1,
        duration: 1.5
      })
      // Stay visible
      .to({}, { duration: 4 })
      // Fade out
      .to(overlayRef.current, {
        opacity: 0,
        duration: 1.5
      });
    });

    return () => ctx.revert();
  }, []);

  return (
    <div className="relative h-[50vw] lg:h-[65%] overflow-hidden group">
      <img
        src={endcake}
        alt="Endcake"
        className="absolute inset-0 w-full h-full object-cover"
      />
      <div 
        ref={overlayRef}
        className="absolute inset-0 bg-black/60 flex items-center justify-center"
      >
        <p className="text-white text-2xl font-bold">
          {text}
        </p>
      </div>
    </div>
  );
};

export default Endcake;