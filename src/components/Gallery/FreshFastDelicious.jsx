// components/Gallery/FreshFastDelicious.jsx
import { useRef, useEffect } from "react";
import { gsap } from "gsap";

const FreshFastDelicious = ({ svg, vector }) => {
  const vectorRef = useRef(null);

  useEffect(() => {
    if (vectorRef.current) {
      gsap.to(vectorRef.current, {
        scale: 1.1,
        repeat: -1,
        yoyo: true,
        duration: 2,
        ease: "power1.inOut",
      });
    }
  }, []);

  return (
    <div className="relative h-[50vw] lg:h-[38%] bg-gray-800/80 flex items-center justify-center">
      <img
        src={svg}
        alt="Fresh Fast Delicious"
        width={150}
        height={150}
        className=" w-[30vw] h-[30vw] lg:w-[200px] lg:h-[200px]"
      />
      <img
        ref={vectorRef}
        src={vector}
        alt=""
        width={180}
        height={180}
        className="absolute w-[50vw] h-[50vw] lg:w-[250px] lg:h-[250px]"
      />
    </div>
  );
};

export default FreshFastDelicious;