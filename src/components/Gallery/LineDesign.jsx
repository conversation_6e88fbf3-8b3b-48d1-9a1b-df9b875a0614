// components/Gallery/LineDesign.jsx
import { useRef, useEffect } from "react";
import { gsap } from "gsap";
import { CSSPlugin } from 'gsap/CSSPlugin';

// Register the CSS plugin
gsap.registerPlugin(CSSPlugin);

const LineDesign = ({ line1, line2, mainText }) => {
  const line1Ref = useRef(null);
  const line2Ref = useRef(null);

  useEffect(() => {
    // First element animation - scales up and down
    if (line1Ref.current) {
      gsap.to(line1Ref.current, {
        scale: 1.2,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
      });
    }
    
    // Second element animation - scales with opposite timing
    if (line2Ref.current) {
      gsap.to(line2Ref.current, {
        scale: 1.2,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
      });
    }
  }, []);

  return (
    <div className="relative w-3/6 bg-gray-800/60 flex items-center justify-center overflow-visible z-10">
      <div className="absolute w-[140%] h-[140%] -left-[20%] overflow-visible">
        <img
          src={line1}
          className="w-full h-full object-contain"
          alt=""
        />
      </div>
      
      {/* First line */}
      <div className="absolute translate-x-2 -translate-y-2 flex justify-center items-center">
        <img
          ref={line1Ref}
          src={line2}
          alt=""
          width={160}
          height={160}
          className="w-[80%] lg:h-[80%] z-10"
        />
      </div>
      
      {/* Second line */}
      <div className="absolute -translate-x-2 translate-y-2 flex justify-center items-center">
        <img
          ref={line2Ref}
          src={line2}
          alt=""
          width={160}
          height={160}
          className="w-[80%] lg:h-[80%] z-10"
        />
      </div>
      
      {/* Text in the center */}
      <img
        src={mainText}
        alt="Main Text"
        width={120}
        height={60}
        className="lg:w-[60%] lg:h-[50%] relative z-20"
      />
    </div>
  );
};

export default LineDesign;