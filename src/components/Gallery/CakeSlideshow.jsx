// components/Gallery/CakeSlideshow.jsx
import { useRef, useEffect } from "react";
import { gsap } from "gsap";

const CakeSlideshow = ({ images }) => {
  const cakeRef = useRef([]);

  useEffect(() => {
    const fadeSlideshow = (refs, delay = 3) => {
      if (!refs.length) return;
      refs.forEach((img, i) => gsap.set(img, { opacity: i === 0 ? 1 : 0 }));
      let index = 0;
      setInterval(() => {
        const prev = refs[index];
        index = (index + 1) % refs.length;
        const next = refs[index];
        gsap.to(prev, { opacity: 0, duration: 1 });
        gsap.to(next, { opacity: 1, duration: 1 });
      }, delay * 1000);
    };

    fadeSlideshow(cakeRef.current);
  }, []);

  return (
    <div className="w-[50%] h-full relative overflow-hidden">
      {images.map((src, i) => (
        <img
          key={i}
          ref={(el) => (cakeRef.current[i] = el)}
          src={src}
          alt="Cake"
          className="absolute inset-0 w-full h-full object-cover"
        />
      ))}
    </div>
  );
};

export default CakeSlideshow;