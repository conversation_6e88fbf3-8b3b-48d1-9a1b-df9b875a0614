// components/Gallery/JuiceSlideshow.jsx
import { useRef, useEffect } from "react";
import { gsap } from "gsap";

const JuiceSlideshow = ({ images }) => {
  const juiceRef = useRef([]);

  useEffect(() => {
    const fadeSlideshow = (refs, delay = 3) => {
      if (!refs.length) return;
      refs.forEach((img, i) => gsap.set(img, { opacity: i === 0 ? 1 : 0 }));
      let index = 0;
      setInterval(() => {
        const prev = refs[index];
        index = (index + 1) % refs.length;
        const next = refs[index];
        gsap.to(prev, { opacity: 0, duration: 1 });
        gsap.to(next, { opacity: 1, duration: 1 });
      }, delay * 1000);
    };

    fadeSlideshow(juiceRef.current);
  }, []);

  return (
    <div className="w-1/2 relative overflow-hidden">
      {images.map((src, i) => (
        <img
          key={i}
          ref={(el) => (juiceRef.current[i] = el)}
          src={src}
          alt="Juice"
          className="absolute inset-0 w-full h-full object-cover"
        />
      ))}
    </div>
  );
};

export default JuiceSlideshow;