import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { useImages } from "../../constants/images";

const Gallery = () => {
  const images = useImages();

  // Debug dan safety check
  console.log("Images data:", images);
  
  // Early return jika data belum loaded
  if (!images || !images.gallery || !images.gallery.imageGallery || !images.gallery.imageGallery.images) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <div className="text-xl">Loading images...</div>
      </div>
    );
  }

  const vectorRef = useRef(null);
  const lineRef = useRef(null);
  const line2Ref = useRef(null);

  // Slideshow refs
  const cakeRef = useRef([]);
  const juiceRef = useRef([]);
  const chefRef = useRef([]);
  const pastaRef = useRef([]);

  useEffect(() => {
    // Vector scale animation
    if (vectorRef.current) {
      gsap.to(vectorRef.current, {
        scale: 1.1,
        repeat: -1,
        yoyo: true,
        duration: 2,
        ease: "power1.inOut",
      });
    }

    // Lines animation
    [lineRef.current, line2Ref.current].forEach((el, idx) => {
      if (el) {
        gsap.to(el, {
          scale: 1.05 + idx * 0.02,
          repeat: -1,
          yoyo: true,
          duration: 2,
          ease: "power1.inOut",
        });
      }
    });

    // Fade slideshow
    const fadeSlideshow = (refs, delay = 3) => {
      if (!refs.length) return;
      refs.forEach((img, i) => gsap.set(img, { opacity: i === 0 ? 1 : 0 }));
      let index = 0;
      setInterval(() => {
        const prev = refs[index];
        index = (index + 1) % refs.length;
        const next = refs[index];
        gsap.to(prev, { opacity: 0, duration: 1 });
        gsap.to(next, { opacity: 1, duration: 1 });
      }, delay * 1000);
    };

    fadeSlideshow(cakeRef.current);
    fadeSlideshow(juiceRef.current);
    fadeSlideshow(pastaRef.current);
    fadeSlideshow(chefRef.current, 4);

    // Slide-up for chef
    chefRef.current.forEach((img, i) => {
      gsap.from(img, {
        y: 50,
        opacity: 0,
        duration: 1,
        delay: i * 0.5,
        ease: "power2.out",
      });
    });
  }, []);

  return (
    <div className="w-[full] flex flex-col justify-center items-center h-[100vh]">
      {/* Header */}
      <div className="relative w-full  h-[150px] lg:h-[250px] mb-4 lg:mb-6 overflow-hidden">
        {images.gallery.imageGallery.images.map((src, i) => (
          <img
            key={i}
            src={src}
            alt=""
            className="absolute inset-0 w-full h-full object-cover"
          />
        ))}
        <img
          src={images.gallery.imageGallery.headerText}
          alt="GALLERY"
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-auto h-auto max-w-[80%] max-h-[90%]"
        />
      </div>

      {/* Main content */}
      <div className="flex flex-col lg:flex-row gap-2 lg:gap-4 lg:h-[1200px] max-w-[120vh] w-[100%] px-2 lg:px-0">
        {/* Column 1 */}
        <div className="flex flex-col gap-2 lg:gap-4 w-full lg:w-1/4">
          {/* Fresh Fast Delicious */}
          <div className="relative h-[150px] lg:h-[30%] bg-blue-900 flex items-center justify-center">
            <img
              src={images.gallery.freshFastDelicious.svg}
              alt="Fresh Fast Delicious"
              width={150}
              height={150}
              className="lg:w-[200px] lg:h-[200px]"
            />
            <img
              ref={vectorRef}
              src={images.gallery.freshFastDelicious.vector}
              alt=""
              width={180}
              height={180}
              className="absolute lg:w-[250px] lg:h-[250px]"
            />
          </div>

          {/* Endcake */}
          <div className="relative h-[250px] lg:h-[65%] overflow-hidden group">
            <img
              src={images.gallery.endcake}
              alt="Endcake"
              className="absolute inset-0 w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition">
              <img
                src={images.gallery.feelFood}
                alt="Feel Food"
                width={120}
                height={120}
                className="absolute inset-0 m-auto lg:w-[150px] lg:h-[150px]"
              />
            </div>
          </div>
        </div>

        {/* Column 2 */}
        <div className="flex flex-col gap-2 lg:gap-4 w-full lg:w-2/4 lg:h-full">
          {/* Row 1 - besar */}
          <div className="flex gap-2 lg:gap-4 ">
            <div className="w-3/6 h-[300px] relative overflow-hidden">
              {images.gallery.cake.map((src, i) => (
                <img
                  key={i}
                  ref={(el) => (cakeRef.current[i] = el)}
                  src={src}
                  alt="Cake"
                  className="absolute inset-0 w-full h-full object-cover"
                />
              ))}
            </div>
        {/* LineDesign */}
        <div className="relative w-3/6 bg-blue-950 flex items-center justify-center overflow-visible">
              <div className="absolute w-[140%] h-[140%] -left-[20%] overflow-visible">
                <img
                  ref={lineRef}
                  src={images.gallery.lineDesign.line}
                  className=" w-full h-full object-contain"
                />
              </div>
            <img
              ref={lineRef}
              src={images.gallery.lineDesign.line2}
              alt=""
              width={160}
              height={160}
              className="absolute lg:w-[220px] lg:h-[220px]"
            />
            <img
              ref={line2Ref}
              src={images.gallery.lineDesign.line2}
              alt=""
              width={160}
              height={160}
              className="absolute top-1 left-1 lg:top-2 lg:left-2 lg:w-[220px] lg:h-[220px]"
            />
            <img
              ref={lineRef}
              src={images.gallery.lineDesign.line}
              className="absolute w-[140%] h-[140%] -left-[10%]"
            />
            <img
              src={images.gallery.lineDesign.mainText}
              alt="Main Text"
              width={120}
              height={60}
              className="lg:w-[150px] lg:h-[80px]"
            />
          </div>
          </div>

          {/* Row 2 - kecil */}
          <div className="flex gap-2 lg:gap-4 h-[50px] lg:h-[15%]">
            <div className="w-2/6 bg-blue-800 flex items-center justify-center p-2">
              <img
                src={images.gallery.discount.text}
                alt="Discount"
                width={120}
                height={50}
                className="lg:w-[180px] lg:h-[80px]"
              />
            </div>
            <div className="w-4/6 bg-blue-900 p-1 lg:p-2 text-white text-[10px] lg:text-xs flex items-center">
              <span className="leading-tight">Food for us comes from our relatives, whether they have wings or fins or roots...</span>
            </div>
          </div>

          {/* Row 3 - besar */}
          <div className="flex gap-2 lg:gap-4 h-[180px] lg:h-[35%]">
            <div className="w-1/2 relative">
              <img
                src={images.gallery.pizza}
                alt="Pizza"
                className="absolute inset-0 w-full h-full object-cover"
              />
            </div>
            <div className="w-1/2 relative overflow-hidden">
              {images.gallery.juice.map((src, i) => (
                <img
                  key={i}
                  ref={(el) => (juiceRef.current[i] = el)}
                  src={src}
                  alt="Juice"
                  className="absolute inset-0 w-full h-full object-cover"
                />
              ))}
            </div>
          </div>
        </div>

        {/* Column 3 */}
        <div className="flex flex-col gap-2 lg:gap-4 w-full lg:w-1/4">
  

          {/* Chef slideshow */}
          <div className="relative h-[180px] lg:h-[60%] overflow-hidden">
            {images.gallery.chef.map((src, i) => (
              <img
                key={i}
                ref={(el) => (chefRef.current[i] = el)}
                src={src}
                alt="Chef"
                className="absolute inset-0 h-full object-cover"
              />
            ))}
          </div>

          {/* Pasta slideshow */}
          <div className="relative h-[180px] lg:h-[40%] overflow-hidden">
            {images.gallery.pasta.map((src, i) => (
              <img
                key={i}
                ref={(el) => (pastaRef.current[i] = el)}
                src={src}
                alt="Pasta"
                className="absolute inset-0 w-full h-full object-cover"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Gallery;