// components/Gallery/ChefSlideshow.jsx
import { useRef, useEffect } from "react";
import { gsap } from "gsap";

const ChefSlideshow = ({ images, className = '' }) => {
  const chefRef = useRef([]);

  useEffect(() => {
    // Set initial state
    chefRef.current.forEach((img, i) => {
      gsap.set(img, { 
        y: '0%',
        opacity: i === 0 ? 1 : 0,
        display: i === 0 ? 'block' : 'none'
      });
    });
  
    let currentIndex = 0;
    const totalImages = images.length;
    let isAnimating = false;
  
    const slideUpNext = () => {
      if (isAnimating) return;
      isAnimating = true;
      
      const nextIndex = (currentIndex + 1) % totalImages;
      const currentImg = chefRef.current[currentIndex];
      const nextImg = chefRef.current[nextIndex];
  
      // Prepare next image below
      gsap.set(nextImg, { 
        display: 'block', 
        y: '100%', 
        opacity: 1 
      });
  
      // Create timeline for animations
      const tl = gsap.timeline({
        onComplete: () => {
          isAnimating = false;
          gsap.set(currentImg, { display: 'none' });
        }
      });
  
      // 1. Slide up new image
      tl.to(nextImg, {
        y: '0%',
        duration: 0.6,
        ease: "power2.inOut"
      });
  
      // 2. Fade out old image after new image is in place
      tl.to(currentImg, { 
        opacity: 0,
        duration: 0.4,
        ease: "power2.inOut",
        delay: 0.6  
      }, '-=0.2'); // Slight overlap
  
      currentIndex = nextIndex;
    };
  
    // Start the slideshow
    const interval = setInterval(slideUpNext, 3000);
  
    return () => {
      clearInterval(interval);
      gsap.killTweensOf(chefRef.current);
    };
  }, [images]);

  return (
    <div className={`relative w-full h-full md:h-[620px] lg:h-[70%] overflow-hidden m-0 p-0 ${className}`} style={{ aspectRatio: '16/9' }}>
      {images.map((src, i) => (
        <img
          key={i}
          ref={(el) => (chefRef.current[i] = el)}
          src={src}
          alt="Chef"
          className="absolute inset-0 w-full h-full object-cover m-0 p-0"
          style={{ objectPosition: 'center center' }}
        />
      ))}
    </div>
  );
};

export default ChefSlideshow;