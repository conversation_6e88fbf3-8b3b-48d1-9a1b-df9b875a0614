// components/Gallery/GalleryHeader.jsx
import { useState, useEffect } from 'react';

const GalleryHeader = ({ images }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % images.images.length);
        setIsTransitioning(false);
      }, 500); // Half of the transition duration for smooth effect
    }, 3000); // Change image every 3 seconds

    return () => clearInterval(interval);
  }, [images.images.length]);

  return (
    <div className="relative w-full h-[150px] lg:h-[230px] mb-4 lg:mb-6 overflow-hidden">
      <div className="relative w-full h-full">
        {images.images.map((src, i) => (
          <img
            key={i}
            src={src}
            alt=""
            className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-500 ${
              i === currentIndex ? 'opacity-100' : 'opacity-0'
            }`}
          />
        ))}
        <img
          src={images.headerText}
          alt="GALLERY"
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-auto h-auto max-w-[80%] max-h-[90%] pointer-events-none"
        />
      </div>
    </div>
  );
};

export default GalleryHeader;