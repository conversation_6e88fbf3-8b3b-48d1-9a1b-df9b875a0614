// components/Gallery/index.jsx
import { useImages } from "../../constants/images";
import GalleryHeader from "./GalleryHeader";
import FreshFastDelicious from "./FreshFastDelicious";
import Endcake from "./Endcake";
import CakeSlideshow from "./CakeSlideshow";
import LineDesign from "./LineDesign";
import DiscountBanner from "./DiscountBanner";
import QuoteBanner from "./QuoteBanner";
import PizzaImage from "./PizzaImage";
import JuiceSlideshow from "./JuiceSlideshow";
import ChefSlideshow from "./ChefSlideshow";
import PastaSlideshow from "./PastaSlideshow";

const Gallery = () => {
  const images = useImages();

  if (!images?.gallery?.imageGallery?.images) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <div className="text-xl">Loading images...</div>
      </div>
    );
  }

  const {
    freshFastDelicious,
    endcake,
    feelFood,
    cake,
    lineDesign,
    discount,
    pizza,
    juice,
    chef,
    pasta,
    backgroundTexture,
  } = images.gallery;

  return (
    <section className="relative">
      <div className="w-[full] flex flex-col justify-center items-center" style={{
      backgroundImage: `url(${images.gallery.backgroundTexture})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      width: '100%',
      minHeight: '100vh'
    }}>
      <GalleryHeader images={images.gallery.imageGallery} />
      
      <div className="flex flex-col lg:flex-row gap-2 lg:gap-4 h-[150vh] md:h-[200vh] lg:h-[80vh] max-w-[140vh] w-[100%] px-2 lg:px-0">
        {/* Column 1 */}
        <div className="flex flex-col gap-2 lg:gap-4 w-full lg:w-1/4">
          <FreshFastDelicious 
            svg={freshFastDelicious.svg} 
            vector={freshFastDelicious.vector} 
          />
          <Endcake
            endcake={endcake} 
            text="feel the taste of real food"
          />
        </div>

        {/* Column 2 */}
        <div className="flex flex-col gap-2 lg:gap-4 w-full lg:w-2/4 lg:h-full">
          {/* Row 1 */}
          <div className="flex w-[100vw] h-[50vw]   gap-2 lg:gap-4 lg:w-[100%] lg:h-3/8">
            <CakeSlideshow images={cake} />
            <LineDesign 
              line1={lineDesign.line} 
              line2={lineDesign.line2} 
              mainText={lineDesign.mainText} 
            />
          </div>

          {/* Row 2 */}
          <div className="flex gap-2 lg:gap-4 h-[150px] md:h-[200px] lg:h-2/8">
            <DiscountBanner text={discount.text} />
            <QuoteBanner 
              text="Food for us comes from our relatives, whether they have wings or fins or roots..."
            />
          </div>

          {/* Row 3 */}
          <div className="flex gap-2 lg:gap-4 h-[180px] md:h-[350px] lg:h-3/8">
            <PizzaImage src={pizza} />
            <JuiceSlideshow images={juice} />
          </div>
        </div>

        {/* Column 3 */}
        <div className="flex flex-col gap-2 lg:gap-4 w-full lg:w-1/4">
          <ChefSlideshow images={chef} />
          <PastaSlideshow images={pasta} />
        </div>

        
      </div>
      <div className="absolute w-full h-full z-10 pointer-events-none top-0">
  <div className="absolute inset-0 bg-black/20"></div>
  <div className="absolute inset-0 bg-gradient-to-br from-transparent via-black/10 to-transparent"></div>
</div>
    </div>
    </section>
  );
};

export default Gallery;