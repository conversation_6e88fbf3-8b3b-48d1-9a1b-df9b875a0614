import { useLayoutEffect, useRef, useState, useCallback, useEffect } from 'react';
import images from '../../constants/images'; // Assuming images.jsx is in the same directory

// Data untuk setiap card
const foodData = [
  {
    id: 0,
    image: "/carousel/carousel-pizza_1-small.png",
    title: "Margherita Pizza",
    subtitle: "Classic & Simple",
    description: "Taste the tradition!",
    quote: "The perfect balance of tomato, mozzarella, and fresh basil."
  },
  {
    id: 1,
    image: "/carousel/carousel-pizza_2-small.png",
    title: "Pepperoni Pizza",
    subtitle: "Bold & Spicy",
    description: "Feel the heat!",
    quote: "America's favorite pizza with crispy pepperoni slices."
  },
  {
    id: 2,
    image: "/carousel/carousel-pizza_3-small.png",
    title: "Veggie Supreme",
    subtitle: "Fresh & Healthy",
    description: "Love the greens!",
    quote: "A garden of fresh vegetables on a crispy crust."
  },
  {
    id: 3,
    image: "/carousel/carousel-pizza_4_small.png",
    title: "Meat Lovers",
    subtitle: "Rich & Hearty",
    description: "Savor the protein!",
    quote: "For those who believe more meat means more flavor."
  },
  {
    id: 4,
    image: "/carousel/carousel-pizza_5-small.png",
    title: "Hawaiian Pizza",
    subtitle: "Sweet & Savory",
    description: "Tropical delight!",
    quote: "The controversial combination that divides pizza lovers."
  },
  {
    id: 5,
    image: "/carousel/carousel-pizza_5-small.png",
    title: "Hawaiian Pizza",
    subtitle: "Sweet & Savory",
    description: "Tropical delight!",
    quote: "The controversial combination that divides pizza lovers."
  },
  {
    id: 6,
    image: "/carousel/carousel-pizza_5-small.png",
    title: "Hawaiian Pizza",
    subtitle: "Sweet & Savory",
    description: "Tropical delight!",
    quote: "The controversial combination that divides pizza lovers."
  }
];

const carouselConfig = {
  desktop: {
    totalCards: 7,
    positionMap: {
      "-3": { x: "-60vw", y: "10vw", scale: 0.78 },
      "-2": { x: "-39vw", y: "8vw", scale: 0.78 },
      "-1": { x: "-22vw", y: "-0.5vw", scale: 0.92 },
      "0":  { x: "0vw",   y: "-5vw", scale: 1.0 },
      "1":  { x: "22vw",  y: "-0.5vw", scale: 0.92 },
      "2":  { x: "39vw",  y: "8vw", scale: 0.78 },
      "3":  { x: "60vw",  y: "10vw", scale: 0.78 }
    }
  },
  mobile: {
    totalCards: 7,
    positionMap: {
      "-3": { x: "-60vw", y: "10vw", scale: 0.78 },
      "-2": { x: "-60vw", y: "8vw", scale: 0.78 },
      "-1": { x: "-35vw", y: "-0.5vw", scale: 1.0 },
      "0":  { x: "0vw",   y: "-5vw", scale: 1.3 },
      "1":  { x: "35vw",  y: "-0.5vw", scale: 1.0 },
      "2":  { x: "60vw",  y: "8vw", scale: 0.78 },
      "3":  { x: "60vw",  y: "10vw", scale: 0.78 }
    }
  }
};

export default function FoodCarousel() {
  const [currentCenter, setCurrentCenter] = useState(2);
  const [isUserInteracting, setIsUserInteracting] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const isAnimating = useRef(false);
  const cardElements = useRef(new Map());
  const [thumbnailsRef, setThumbnailsRef] = useState(null);
  const [thumbnailsContainerRef, setThumbnailsContainerRef] = useState(null);

  const config = isMobile ? carouselConfig.mobile : carouselConfig.desktop;
  const { totalCards, positionMap } = config;

  const baseSize = 20;

  useEffect(() => {
    const checkMobile = () => {
      const mobileState = window.innerWidth < 768;
      if (isMobile !== mobileState) {
        setIsMobile(mobileState);
        // Reset center to a valid card index when switching modes
        const newTotalCards = mobileState ? carouselConfig.mobile.totalCards : carouselConfig.desktop.totalCards;
        setCurrentCenter(prev => prev % newTotalCards);
      }
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [isMobile]);

  const setCardRef = useCallback((cardId) => (el) => {
    if (el) cardElements.current.set(cardId, el);
    else cardElements.current.delete(cardId);
  }, []);

  const computeCardPosition = useCallback((cardId) => {
    const offset = cardId - currentCenter;
    const half = Math.floor(totalCards / 2);
    let normalizedOffset = (offset % totalCards);
    if (normalizedOffset > half) {
      normalizedOffset -= totalCards;
    } else if (normalizedOffset < -half) {
      normalizedOffset += totalCards;
    }

    const { x, y, scale } = positionMap[normalizedOffset.toString()] || { x: "0vw", y: "10vw", scale: 0 };

    const size = baseSize * scale;
    const opacity = scale === 0 ? 0 : 1;

    return {
      x,
      y,
      z: 0,
      scale,
      opacity,
      zIndex: Math.round(scale * 10),
      size
    };
  }, [currentCenter, totalCards, positionMap]);

  const applyCardTransformations = useCallback((duration = 0.6) => {
    if (isAnimating.current) return;
    isAnimating.current = true;

    for (const [cardId, cardEl] of cardElements.current.entries()) {
      if (!cardEl) continue;
      const { x, y, z, scale, opacity, zIndex, size } = computeCardPosition(cardId);
      cardEl.style.transform = `translateX(-50%) translate3d(${x}, ${y}, ${z}px) scale(${scale})`;
      cardEl.style.opacity = opacity;
      cardEl.style.zIndex = zIndex;
      cardEl.style.width = `${size}vw`;
      cardEl.style.height = `${size}vw`;
    }

    setTimeout(() => {
      isAnimating.current = false;
    }, duration * 1000);
  }, [computeCardPosition]);

  const navigate = useCallback((direction) => {
    if (isAnimating.current) return;
    setIsUserInteracting(true);

    const half = Math.floor(totalCards / 2);
    const jumpingOffset = direction === 'next' ? -half : half;

    // Find which card is about to jump
    let jumpingCardId = -1;
    for (const cardId of cardElements.current.keys()) {
      const offset = cardId - currentCenter;
      let normalizedOffset = offset % totalCards;
      if (normalizedOffset > half) normalizedOffset -= totalCards;
      else if (normalizedOffset < -half) normalizedOffset += totalCards;

      if (normalizedOffset === jumpingOffset) {
        jumpingCardId = cardId;
        break;
      }
    }

    // Temporarily disable transition for the jumping card
    if (jumpingCardId !== -1) {
      const cardEl = cardElements.current.get(jumpingCardId);
      if (cardEl) {
        cardEl.style.transition = 'none';
        cardEl.style.opacity = 0;
      }
    }

    // Update the center card
    setCurrentCenter(prev => {
      let newCenter = direction === 'next' ? (prev + 1) % totalCards : (prev - 1 + totalCards) % totalCards;
      return newCenter;
    });

    // Restore transition for the jumping card after it has moved
    setTimeout(() => {
      if (jumpingCardId !== -1) {
        const cardEl = cardElements.current.get(jumpingCardId);
        if (cardEl) {
          cardEl.style.transition = 'transform 0.6s ease, opacity 0.6s ease';
        }
      }
      setIsUserInteracting(false);
    }, 50); // A small delay to allow the DOM to update

  }, [currentCenter, totalCards]);

  const next = useCallback(() => navigate('next'), [navigate]);
  const prev = useCallback(() => navigate('prev'), [navigate]);

  useEffect(() => {
    applyCardTransformations();
  }, [currentCenter, applyCardTransformations]);

  useLayoutEffect(() => {
    applyCardTransformations(0);
  }, [applyCardTransformations]);

  // Scroll to center the active thumbnail
  useEffect(() => {
    if (!thumbnailsRef || !thumbnailsContainerRef) return;
    
    const container = thumbnailsContainerRef;
    const activeThumb = thumbnailsRef.children[currentCenter];
    
    if (!activeThumb) return;
    
    const containerWidth = container.offsetWidth;
    const thumbWidth = activeThumb.offsetWidth + 12; // 12px for gap (space-x-3)
    const scrollLeft = activeThumb.offsetLeft - (containerWidth / 2) + (thumbWidth / 2);
    
    container.scrollTo({
      left: scrollLeft,
      behavior: 'smooth'
    });
  }, [currentCenter]);

  return (
    <div className='flex flex-col bg-gray-50 h-full px-6 md:px-0'>
      {/* Main Content */}
            {/* More Details Button */}
            <div className='flex justify-center py-5 pb-8 md:py-8'>
        <button className='bg-white rounded-full px-8 py-3 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:bg-gray-50 active:scale-95'>
          <span className='text-gray-900 font-semibold font-montserrat'>More Details</span>
        </button>
      </div>
      <div className='flex-1 flex flex-col'>
        {/* Mobile/Tablet Layout */}
        <div className='md:hidden flex flex-col items-center pb-4 px-4 h-full relative'>
          {/* Background Image for Mobile */}
          <div className='absolute inset-0 bg-cover bg-center ' 
               style={{ transform: 'rotate(180deg)', width:'100%',height:'30%',backgroundImage: `url(${images.carousel.bg})` }}></div>
          {/* Main Image with Background */}
          <div className='relative w-full max-w-[320px] mx-auto mt-6 '>
            <div className='absolute inset-0 bg-gradient-to-br from-orange-50 to-amber-50 rounded-full -z-10 scale-110'></div>
            <div className='relative w-full aspect-square p-6'>
              <div className='w-full h-full overflow-hidden rounded-full shadow-lg bg-white'>
                <img
                  src={foodData[currentCenter].image}
                  alt={foodData[currentCenter].title}
                  className='w-full h-full object-contain transition-opacity duration-500'
                  style={{
                    opacity: 1,
                    transform: 'scale(1)'
                  }}
                  key={currentCenter}
                />
              </div>
              
              {/* Navigation Arrows */}
              <div className='absolute top-1/2 left-0 right-0 -translate-y-1/2 flex justify-between px-2 z-10'>
                <button 
                  onClick={prev}
                  className='w-12 h-12 bg-white/95 backdrop-blur-sm rounded-full shadow-md flex items-center justify-center active:scale-95 transition-transform hover:scale-105'
                >
                  <svg className='w-6 h-6 text-gray-700' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M15 19l-7-7 7-7' />
                  </svg>
                </button>
                <button 
                  onClick={next}
                  className='w-12 h-12 bg-white/95 backdrop-blur-sm rounded-full shadow-md flex items-center justify-center active:scale-95 transition-transform hover:scale-105'
                >
                  <svg className='w-6 h-6 text-gray-700' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M9 5l7 7-7 7' />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Info Panel */}
          <div className='bg-white/95 backdrop-blur-md rounded-3xl p-6 w-full max-w-md mx-auto shadow-lg mt-0 pt-0'>
            <div className='text-center mb-5'>
              <h2 className='text-2xl font-extrabold text-gray-900 mb-1 font-poppins'>{foodData[currentCenter].title}</h2>
              <h2 className='text-2xl font-extrabold text-gray-900 mb-1 font-poppins'>{foodData[currentCenter].subtitle}</h2>
              <h2 className='text-2xl font-extrabold text-gray-900 mb-1 font-poppins'>{foodData[currentCenter].description}</h2>
              <p className='text-gray-600 font-extrabold italic mb-4 text-sm md:text-base font-poppins'>"{foodData[currentCenter].quote}"</p>

            </div>
            
            {/* Thumbnails */}
            <div className='relative mt-6'>
              <div 
                ref={setThumbnailsContainerRef}
                className='overflow-x-auto pb-3 -mx-2 scrollbar-hide scroll-smooth p-[10px] bg-gray-500 rounded-md'
                style={{ scrollBehavior: 'smooth' }}
              >
                <div 
                  ref={setThumbnailsRef}
                  className='flex space-x-3 w-max mx-auto px-2'
                >
                  {foodData.map((item, index) => (
                    <button
                      key={item.id}
                      onClick={() => setCurrentCenter(index)}
                      className={`w-14 h-14 rounded-full overflow-hidden flex-shrink-0 transition-all duration-300 transform ${
                        index === currentCenter 
                          ? 'ring-3 ring-orange-500 scale-110 shadow-md' 
                          : 'opacity-80 hover:opacity-100 hover:scale-105'
                      }`}
                    >
                      <img
                        src={item.image}
                        alt={item.title}
                        className='w-full h-full object-cover'
                      />
                    </button>
                  ))}
                </div>
              </div>
              {/* Dots indicator */}
              <div className='flex justify-center gap-2 mt-4'>
                {foodData.map((_, index) => (
                  <div 
                    key={index}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === currentCenter ? 'bg-orange-500 w-6' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Desktop Layout (unchanged) */}
        <div className='hidden md:block w-full'>
          {/* Existing desktop carousel code */}
          <div
            className="relative w-full h-full flex flex-col md:pt-20 lg:mt-25 xl:mt-30 2xl:mt-40 pb-0 md:pb-10 md:h-[55vw] lg:h-[50vw] xl:h-[45vw] items-center justify-start bg-gray-50"
            style={{
              backgroundImage: 'url(/carousel/bg2-carousel.webp)',
              backgroundSize: '100% 100%',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat'
            }}
          >

            <div className="relative w-full h-full px-4" style={{ perspective: '1200px' }}>
              <div className={`relative z-10 ${isMobile ? 'h-[250px]' : 'h-[380px]'} mb-6 md:mb-8 flex items-center justify-center`}>
                {[...Array(totalCards).keys()].map((cardId) => {
                  const position = computeCardPosition(cardId);
                  return (
                    <div
                      key={cardId}
                      ref={setCardRef(cardId)}
                      className="card-item absolute left-1/2 top-0 bg-gray-200 rounded-full shadow-md cursor-pointer"
                      style={{
                        width: `${position.size}vw`,
                        height: `${position.size}vw`,
                        transform: `translateX(-50%) translate3d(${position.x}, ${position.y}, ${position.z}px) scale(${position.scale})`,
                        willChange: 'transform, opacity',
                        transformStyle: 'preserve-3d',
                        transition: 'transform 0.6s ease, opacity 0.6s ease',
                        opacity: position.opacity,
                        zIndex: position.zIndex,
                      }}
                      onClick={() => {
                        if (cardId !== currentCenter && !isAnimating.current) {
                          setIsUserInteracting(true);
                          
                          // PERBAIKAN: Hanya hide cards yang benar-benar "jump" lintas sisi
                          const half = Math.floor(totalCards / 2);
                          const jumpingCards = [];
                          
                          // Hitung hanya cards yang akan jump dari satu ujung ke ujung lain
                          for (const id of cardElements.current.keys()) {
                            const currentOffset = id - currentCenter;
                            let currentNormalizedOffset = currentOffset % totalCards;
                            if (currentNormalizedOffset > half) currentNormalizedOffset -= totalCards;
                            else if (currentNormalizedOffset < -half) currentNormalizedOffset += totalCards;
                            
                            const newOffset = id - cardId;
                            let newNormalizedOffset = newOffset % totalCards;
                            if (newNormalizedOffset > half) newNormalizedOffset -= totalCards;
                            else if (newNormalizedOffset < -half) newNormalizedOffset += totalCards;
                            
                            // Hanya hide card yang jump dari ujung ke ujung (misal dari posisi 3 ke -3, atau -3 ke 3)
                            const currentPos = positionMap[currentNormalizedOffset.toString()];
                            const newPos = positionMap[newNormalizedOffset.toString()];
                            
                            if (currentPos && newPos) {
                              // Hanya hide jika card melompat dari ujung kanan ke ujung kiri atau sebaliknya
                              // Dan hanya jika kedua posisi itu adalah posisi ujung (scale kecil)
                              if (Math.abs(currentNormalizedOffset) >= 2 && Math.abs(newNormalizedOffset) >= 2) {
                                // Card di ujung yang akan pindah ke ujung lain
                                if ((currentNormalizedOffset >= 2 && newNormalizedOffset <= -2) || 
                                    (currentNormalizedOffset <= -2 && newNormalizedOffset >= 2)) {
                                  jumpingCards.push(id);
                                }
                              }
                            }
                            
                            // Atau card yang akan hilang sama sekali (ke posisi invisible)
                            if (!newPos || newPos.scale === 0) {
                              jumpingCards.push(id);
                            }
                          }
                          
                          // Hide hanya cards yang benar-benar jump
                          jumpingCards.forEach(id => {
                            const cardEl = cardElements.current.get(id);
                            if (cardEl) {
                              cardEl.style.transition = 'none';
                              cardEl.style.opacity = 0;
                            }
                          });
                          
                          setCurrentCenter(cardId);
                          
                          // Restore transitions after DOM update
                          setTimeout(() => {
                            jumpingCards.forEach(id => {
                              const cardEl = cardElements.current.get(id);
                              if (cardEl) {
                                cardEl.style.transition = 'transform 0.6s ease, opacity 0.6s ease';
                              }
                            });
                            setIsUserInteracting(false);
                          }, 100);
                        }
                      }}
                    >
                      <div className="w-full h-full flex items-center justify-center">
                        <img
                          src={foodData[cardId].image}
                          alt={foodData[cardId].title}
                          className="w-full h-full object-cover rounded-full"
                        />
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Panel Overlay */}
              <div className="bg-white/30 pt-[5vw]  bottom-[10%] md:bottom-[1%] lg:bottom-[10%] h-[100%] w-[70vw] backdrop-blur-[20px] absolute  md:w-[350px] md:h-[40vh] left-1/2 transform -translate-x-1/2 z-[1] rounded-b-[50px] text-center flex flex-col justify-start py-4 px-6">
                <div className="absolute bottom-[10px] md:bottom-[1%] xl:bottom-[5vw] left-0 right-0 p-6">
                  <h2 className="text-xl md:text-2xl font-bold text-gray-900">{foodData[currentCenter].title}</h2>
                  <h3 className="text-lg md:text-2xl font-bold text-gray-900">{foodData[currentCenter].subtitle}</h3>
                  <h4 className="text-lg md:text-2xl font-bold text-gray-900 mb-2">{foodData[currentCenter].description}</h4>
                  <blockquote className="text-gray-800 italic text-sm md:text-base leading-tight">"{foodData[currentCenter].quote}"</blockquote>
                </div>
               </div>
              {/* Kontrol Navigasi */}
              <div
                className={`w-[95vw] md:w-[500px] lg:w-[650px] z-[110] absolute left-1/2 transform -translate-x-1/2 bottom-1/2 translate-y-1/2 md:bottom-1/3 md:translate-y-1/3 lg:bottom-1/3 lg:translate-y-1/3 flex justify-between items-center max-w-2xl mx-auto ${isMobile ? 'gap-x-2' : 'gap-x-4'}` }
              >
                <button 
                  onClick={prev} 
                  className="relative z-[110] flex-shrink-0 w-10 h-10 md:w-12 md:h-12 lg:w-15 lg:h-15 bg-white/30 backdrop-blur-md rounded-full shadow-md flex items-center justify-center text-gray-400 transition-transform active:scale-90 hover:scale-105"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>

                <button 
                  onClick={next} 
                  className="relative z-[110] flex-shrink-0 w-10 h-10 md:w-12 md:h-12 lg:w-15 lg:h-15 bg-white/30 backdrop-blur-md rounded-full shadow-md flex items-center justify-center text-gray-400 transition-transform active:scale-90 hover:scale-105"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
}