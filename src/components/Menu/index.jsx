import React, { lazy, Suspense } from 'react';
import PropTypes from 'prop-types';

// Lazy load components with error boundaries
const ListMenu = lazy(() => 
  import(/* webpackChunkName: "list-menu" */ './ListMenu')
    .catch(() => ({ default: () => (
      <div className="text-center p-4 text-red-600">
        Failed to load menu items. Please try again later.
      </div>
    )}))
);

const Slider = lazy(() => 
  import(/* webpackChunkName: "slider" */ './Slider')
    .catch(() => ({ default: () => (
      <div className="text-center p-4 text-red-600">
        Failed to load featured items. Please try again later.
      </div>
    )}))
);

// Loading component with animation
const LoadingSpinner = () => (
  <div className="flex items-center justify-center py-12">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
  </div>
);

// Error Boundary component
class ErrorBoundary extends React.Component {
  state = { hasError: false, error: null };

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Menu Error:', error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="text-center p-6 bg-red-50 rounded-lg my-4">
          <h3 className="text-lg font-medium text-red-800 mb-2">Something went wrong</h3>
          <p className="text-red-600 mb-4">
            {this.state.error?.message || 'Failed to load menu section'}
          </p>
          <button
            onClick={this.handleRetry}
            className="px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

ErrorBoundary.propTypes = {
  children: PropTypes.node.isRequired
};

const MenuSection = () => {
  return (
    <section className="px-0 py-0 bg-gray-50 pb-0">
      {/* Our Menu Badge */}
      <div className="flex justify-center mb-8 px-6">
        <div className="flex flex-row gap-2 items-center bg-white rounded-full px-6 py-3 border border-gray-200 shadow-[inset_4px_5px_8px_rgb(197,192,192),inset_-6px_-6px_5px_rgb(255,255,255)]">
          <svg 
            width="24" 
            height="25" 
            viewBox="0 0 24 25" 
            fill="none" 
            xmlns="http://www.w3.org/2000/svg"
            aria-hidden="true"
            focusable="false"
          >
            <g clipPath="url(#clip0_134_2285)">
              <path 
                d="M12.002 0.5C5.38395 0.5 0 5.883 0 12.5C0 19.117 5.383 24.5 12 24.5C13.788 24.5 15.476 24.0898 17 23.3848V21.1328C15.528 21.9928 13.826 22.5 12 22.5C6.486 22.5 2 18.014 2 12.5C2 6.986 6.486 2.5 12 2.5C13.041 2.5 14.0375 2.67661 14.9805 2.97461C15.2975 2.34461 15.6877 1.76567 16.1387 1.26367C14.8467 0.784672 13.46 0.5 12.002 0.5ZM20 1.41211C18.677 1.41211 17.6749 2.33285 17.0254 3.39844C16.3759 4.46402 16 5.77292 16 7.08008C16 8.38723 16.4153 9.50697 17.1621 10.2734C17.6657 10.7902 18.3164 11.063 19 11.2285V23.5H21V11.2285C21.6836 11.063 22.3343 10.7902 22.8379 10.2734C23.5847 9.50697 24 8.38723 24 7.08008C24 5.77292 23.6241 4.46402 22.9746 3.39844C22.3251 2.33285 21.323 1.41211 20 1.41211ZM20 3.41211C20.334 3.41211 20.8313 3.72379 21.2676 4.43945C21.7038 5.15512 22 6.17823 22 7.08008C22 7.98192 21.7432 8.52917 21.4043 8.87695C21.0654 9.22474 20.6024 9.41211 20 9.41211C19.3976 9.41211 18.9346 9.22474 18.5957 8.87695C18.2568 8.52917 18 7.98192 18 7.08008C18 6.17823 18.2962 5.15512 18.7324 4.43945C19.1687 3.72379 19.666 3.41211 20 3.41211ZM12 4.5C7.589 4.5 4 8.089 4 12.5C4 16.911 7.589 20.5 12 20.5V18.5C8.691 18.5 6 15.809 6 12.5C6 9.191 8.691 6.5 12 6.5C12.706 6.5 13.3808 6.62952 14.0098 6.85352C14.0278 6.17852 14.1211 5.49889 14.2891 4.83789C13.5631 4.62089 12.796 4.5 12 4.5Z" 
                fill="#FF8243"
                aria-label="Menu icon"
              />
            </g>
            <defs>
              <clipPath id="clip0_134_2285">
                <rect width="24" height="24" fill="white" transform="translate(0 0.5)" />
              </clipPath>
            </defs>
          </svg>

          <span className="text-orange-500 text-[1rem] font-medium font-montserrat">
            Our Menu
          </span>
        </div>
      </div>

      <ErrorBoundary>
        <Suspense fallback={<LoadingSpinner />}>
          <ListMenu />
        </Suspense>
      </ErrorBoundary>

      <ErrorBoundary>
        <Suspense fallback={<LoadingSpinner />}>
          <Slider />
        </Suspense>
      </ErrorBoundary>
    </section>
  );
};

MenuSection.propTypes = {
  // Add any props here if needed in the future
};

export default MenuSection;