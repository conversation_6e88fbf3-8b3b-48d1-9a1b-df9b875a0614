const ProductCard = ({ title, img, price, description }) => (
  <div className="relative bg-white rounded-3xl shadow-lg p-8 pt-35 mt-30 flex flex-col items-center text-center hover:shadow-xl transition-shadow">
    {/* Circular Image */}
    <div className="absolute top-[-120px] w-60 h-60 mb-6 overflow-hidden rounded-full shadow-md">
      <img
        className="w-full h-full object-cover"
        src={img}
        alt={title}
        onError={(e) => {
          e.target.src = 'https://placehold.co/300x300/f3f4f6/9ca3af?text=Food+Image';
        }}
      />
    </div>

    {/* Title */}
    <h3 className="text-xl font-bold mb-4 text-gray-800 font-playfair">{title}</h3>

    {/* Description */}
    <p className="text-lg text-gray-600 mb-6 leading-relaxed px-2  font-lora font-semibold">
      {description}
    </p>

    {/* Price */}
    <span className="text-lg font-bold text-gray-800 font-poppins">{price}</span>
  </div>
);
export default ProductCard;