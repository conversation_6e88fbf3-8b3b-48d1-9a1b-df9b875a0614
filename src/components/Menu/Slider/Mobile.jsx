import { useRef, useState, useCallback } from 'react';
import images from '../../../constants/images';

export default function SliderMobile({ foodData, currentCenter, setCurrentCenter }) {
  const cardElements = useRef(new Map());
  const [thumbnailsRef, setThumbnailsRef] = useState(null);
  const [thumbnailsContainerRef, setThumbnailsContainerRef] = useState(null);

  const navigate = useCallback((direction) => {
    setCurrentCenter(prev => {
      const totalCards = foodData.length;
      return direction === 'next' 
        ? (prev + 1) % totalCards 
        : (prev - 1 + totalCards) % totalCards;
    });
  }, [foodData.length]);

  const next = useCallback(() => navigate('next'), [navigate]);
  const prev = useCallback(() => navigate('prev'), [navigate]);

  return (
    <div className='flex flex-col bg-gray-50 h-full px-6 md:px-0'>
      {/* More Details Button */}
      <div className='flex justify-center py-5 pb-8 md:py-8'>
        <button className='bg-white rounded-full px-8 py-3 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:bg-gray-50 active:scale-95'>
          <span className='text-gray-900 font-semibold font-montserrat'>More Details</span>
        </button>
      </div>
      
      <div className='flex-1 flex flex-col'>
        <div className='flex flex-col items-center pb-4 px-4 h-full relative'>
          {/* Background Image for Mobile */}
          <div 
            className='absolute inset-0 bg-cover bg-center' 
            style={{ 
              transform: 'rotate(180deg)', 
              width: '100%',
              height: '30%',
              backgroundImage: `url(${images.carousel.bg})` 
            }}
          ></div>
          
          {/* Main Image with Background */}
          <div className='relative w-full max-w-[320px] mx-auto mt-6'>
            <div className='absolute inset-0 bg-gradient-to-br from-orange-50 to-amber-50 rounded-full -z-10 scale-110'></div>
            <div className='relative w-full aspect-square p-6'>
              <div className='w-full h-full overflow-hidden rounded-full shadow-lg bg-white'>
                <img
                  src={foodData[currentCenter].image}
                  alt={foodData[currentCenter].title}
                  className='w-full h-full object-contain transition-opacity duration-500'
                  style={{
                    opacity: 1,
                    transform: 'scale(1)'
                  }}
                  key={currentCenter}
                />
              </div>
              
              {/* Navigation Arrows */}
              <div className='absolute top-1/2 left-0 right-0 -translate-y-1/2 flex justify-between px-2 z-10'>
                <button 
                  onClick={prev}
                  className='w-12 h-12 bg-white/95 backdrop-blur-sm rounded-full shadow-md flex items-center justify-center active:scale-95 transition-transform hover:scale-105'
                >
                  <svg className='w-6 h-6 text-gray-700' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M15 19l-7-7 7-7' />
                  </svg>
                </button>
                <button 
                  onClick={next}
                  className='w-12 h-12 bg-white/95 backdrop-blur-sm rounded-full shadow-md flex items-center justify-center active:scale-95 transition-transform hover:scale-105'
                >
                  <svg className='w-6 h-6 text-gray-700' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M9 5l7 7-7 7' />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Info Panel */}
          <div className='bg-white/95 backdrop-blur-md rounded-3xl p-6 w-full max-w-md mx-auto shadow-lg mt-0 pt-0'>
            <div className='text-center mb-5'>
              <h2 className='text-2xl font-extrabold text-gray-900 mb-1 font-poppins'>{foodData[currentCenter].title}</h2>
              <h2 className='text-2xl font-extrabold text-gray-900 mb-1 font-poppins'>{foodData[currentCenter].subtitle}</h2>
              <h2 className='text-2xl font-extrabold text-gray-900 mb-1 font-poppins'>{foodData[currentCenter].description}</h2>
              <p className='text-gray-800 italic mb-4 text-sm md:text-base font-poppins'>"{foodData[currentCenter].quote}"</p>
            </div>
            
            {/* Thumbnails */}
            <div className='relative mt-6'>
              <div 
                ref={setThumbnailsContainerRef}
                className='overflow-x-auto pb-3 -mx-2 scrollbar-hide scroll-smooth p-[10px] bg-gray-500 rounded-md'
                style={{ scrollBehavior: 'smooth' }}
              >
                <div 
                  ref={setThumbnailsRef}
                  className='flex space-x-3 w-max mx-auto px-2'
                >
                  {foodData.map((item, index) => (
                    <button
                      key={item.id}
                      onClick={() => setCurrentCenter(index)}
                      className={`w-14 h-14 rounded-full overflow-hidden flex-shrink-0 transition-all duration-300 transform ${
                        index === currentCenter 
                          ? 'ring-3 ring-orange-500 scale-110 shadow-md' 
                          : 'opacity-80 hover:opacity-100 hover:scale-105'
                      }`}
                    >
                      <img
                        src={item.image}
                        alt={item.title}
                        className='w-full h-full object-cover'
                      />
                    </button>
                  ))}
                </div>
              </div>
              {/* Dots indicator */}
              <div className='flex justify-center gap-2 mt-4'>
                {foodData.map((_, index) => (
                  <div 
                    key={index}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === currentCenter ? 'bg-orange-500 w-6' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
