import { useState, useEffect } from 'react';
import SliderMobile from './Mobile';
import SliderDesktop from './Desktop';
import { foodData } from './data';

export default function Slider() {
  const [currentCenter, setCurrentCenter] = useState(2);
  const [isMobile, setIsMobile] = useState(false);

  // Handle responsive behavior
  useEffect(() => {
    const checkMobile = () => {
      const mobileState = window.innerWidth < 768;
      if (isMobile !== mobileState) {
        setIsMobile(mobileState);
        // Reset to a valid center when switching modes
        setCurrentCenter(prev => prev % foodData.length);
      }
    };
    
    // Initial check
    checkMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, [isMobile]);

  return (
    <>
      {isMobile ? (
        <SliderMobile 
          foodData={foodData}
          currentCenter={currentCenter}
          setCurrentCenter={setCurrentCenter}
        />
      ) : (
        <SliderDesktop 
          foodData={foodData}
          currentCenter={currentCenter}
          setCurrentCenter={setCurrentCenter}
        />
      )}
    </>
  );
}