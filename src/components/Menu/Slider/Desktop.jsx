import { useRef, useCallback, useEffect, useLayoutEffect, useState } from 'react';

export default function SliderDesktop({ foodData, currentCenter, setCurrentCenter }) {
  const [isUserInteracting, setIsUserInteracting] = useState(false);
  const isAnimating = useRef(false);
  const cardElements = useRef(new Map());
  
  const config = {
    totalCards: 7,
    positionMap: {
      "-3": { x: "-60vw", y: "10vw", scale: 0.78 },
      "-2": { x: "-39vw", y: "8vw", scale: 0.78 },
      "-1": { x: "-22vw", y: "-0.5vw", scale: 0.92 },
      "0":  { x: "0vw",   y: "-5vw", scale: 1.0 },
      "1":  { x: "22vw",  y: "-0.5vw", scale: 0.92 },
      "2":  { x: "39vw",  y: "8vw", scale: 0.78 },
      "3":  { x: "60vw",  y: "10vw", scale: 0.78 }
    }
  };

  const baseSize = 20;
  const { totalCards, positionMap } = config;

  const setCardRef = useCallback((cardId) => (el) => {
    if (el) cardElements.current.set(cardId, el);
    else cardElements.current.delete(cardId);
  }, []);

  const computeCardPosition = useCallback((cardId) => {
    const offset = cardId - currentCenter;
    const half = Math.floor(totalCards / 2);
    let normalizedOffset = (offset % totalCards);
    if (normalizedOffset > half) {
      normalizedOffset -= totalCards;
    } else if (normalizedOffset < -half) {
      normalizedOffset += totalCards;
    }

    const { x, y, scale } = positionMap[normalizedOffset.toString()] || { x: "0vw", y: "10vw", scale: 0 };
    const size = baseSize * scale;
    const opacity = scale === 0 ? 0 : 1;

    return {
      x,
      y,
      z: 0,
      scale,
      opacity,
      zIndex: Math.round(scale * 10),
      size
    };
  }, [currentCenter, totalCards, positionMap]);

  const applyCardTransformations = useCallback((duration = 0.6) => {
    if (isAnimating.current) return;
    isAnimating.current = true;

    for (const [cardId, cardEl] of cardElements.current.entries()) {
      if (!cardEl) continue;
      const { x, y, z, scale, opacity, zIndex, size } = computeCardPosition(cardId);
      cardEl.style.transform = `translateX(-50%) translate3d(${x}, ${y}, ${z}px) scale(${scale})`;
      cardEl.style.opacity = opacity;
      cardEl.style.zIndex = zIndex;
      cardEl.style.width = `${size}vw`;
      cardEl.style.height = `${size}vw`;
    }

    setTimeout(() => {
      isAnimating.current = false;
    }, duration * 1000);
  }, [computeCardPosition]);

  const navigate = useCallback((direction) => {
    if (isAnimating.current) return;
    setIsUserInteracting(true);

    const half = Math.floor(totalCards / 2);
    const jumpingOffset = direction === 'next' ? -half : half;

    // Find which card is about to jump
    let jumpingCardId = -1;
    for (const cardId of cardElements.current.keys()) {
      const offset = cardId - currentCenter;
      let normalizedOffset = offset % totalCards;
      if (normalizedOffset > half) normalizedOffset -= totalCards;
      else if (normalizedOffset < -half) normalizedOffset += totalCards;

      if (normalizedOffset === jumpingOffset) {
        jumpingCardId = cardId;
        break;
      }
    }

    // Temporarily disable transition for the jumping card
    if (jumpingCardId !== -1) {
      const cardEl = cardElements.current.get(jumpingCardId);
      if (cardEl) {
        cardEl.style.transition = 'none';
        cardEl.style.opacity = 0;
      }
    }

    // Update the center card
    setCurrentCenter(prev => {
      let newCenter = direction === 'next' ? (prev + 1) % totalCards : (prev - 1 + totalCards) % totalCards;
      return newCenter;
    });

    // Restore transition for the jumping card after it has moved
    setTimeout(() => {
      if (jumpingCardId !== -1) {
        const cardEl = cardElements.current.get(jumpingCardId);
        if (cardEl) {
          cardEl.style.transition = 'transform 0.6s ease, opacity 0.6s ease';
        }
      }
      setIsUserInteracting(false);
    }, 50);
  }, [currentCenter, totalCards]);

  const next = useCallback(() => navigate('next'), [navigate]);
  const prev = useCallback(() => navigate('prev'), [navigate]);

  useEffect(() => {
    applyCardTransformations();
  }, [currentCenter, applyCardTransformations]);

  useLayoutEffect(() => {
    applyCardTransformations(0);
  }, [applyCardTransformations]);

  return (
    <div className="w-full">
      <div 
        className="relative w-full h-full flex flex-col md:pt-20 lg:mt-25 xl:mt-30 2xl:mt-40 pb-0 md:pb-10 md:h-[55vw] lg:h-[50vw] xl:h-[45vw] items-center justify-start bg-gray-50"
        style={{
          backgroundImage: 'url(/carousel/bg2-carousel.webp)',
          backgroundSize: '100% 100%',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        <div className="relative w-full h-full px-4" style={{ perspective: '1200px' }}>
          <div className={`relative z-10 h-[380px] mb-6 md:mb-8 flex items-center justify-center`}>
            {[...Array(totalCards).keys()].map((cardId) => {
              const position = computeCardPosition(cardId);
              return (
                <div
                  key={cardId}
                  ref={setCardRef(cardId)}
                  className="card-item absolute left-1/2 top-0 bg-gray-200 rounded-full shadow-md cursor-pointer"
                  style={{
                    width: `${position.size}vw`,
                    height: `${position.size}vw`,
                    transform: `translateX(-50%) translate3d(${position.x}, ${position.y}, ${position.z}px) scale(${position.scale})`,
                    willChange: 'transform, opacity',
                    transformStyle: 'preserve-3d',
                    transition: 'transform 0.6s ease, opacity 0.6s ease',
                    opacity: position.opacity,
                    zIndex: position.zIndex,
                  }}
                  onClick={() => {
                    if (cardId !== currentCenter && !isAnimating.current) {
                      setCurrentCenter(cardId);
                    }
                  }}
                >
                  <div className="w-full h-full flex items-center justify-center">
                    <img
                      src={foodData[cardId].image}
                      alt={foodData[cardId].title}
                      className="w-full h-full object-cover rounded-full"
                    />
                  </div>
                </div>
              );
            })}
          </div>

          {/* Panel Overlay */}
          <div className="bg-white/30 pt-[5vw] bottom-[10%] md:bottom-[1%] lg:bottom-[10%] h-[100%] w-[70vw] backdrop-blur-[20px] absolute md:w-[350px] md:h-[40vh] left-1/2 transform -translate-x-1/2 z-[1] rounded-b-[50px] text-center flex flex-col justify-start py-4 px-6">
            <div className="absolute bottom-[10px] md:bottom-[1%] xl:bottom-[5vw] left-0 right-0 p-6">
              <h2 className="text-xl md:text-2xl font-bold text-gray-900">{foodData[currentCenter].title}</h2>
              <h3 className="text-lg md:text-2xl font-bold text-gray-900">{foodData[currentCenter].subtitle}</h3>
              <h4 className="text-lg md:text-2xl font-bold text-gray-900 mb-2">{foodData[currentCenter].description}</h4>
              <blockquote className="text-gray-800 italic text-sm md:text-base leading-tight">"{foodData[currentCenter].quote}"</blockquote>
            </div>
          </div>
          
          {/* Navigation Controls */}
          <div className="w-[95vw] md:w-[500px] lg:w-[650px] z-[110] absolute left-1/2 transform -translate-x-1/2 bottom-1/2 translate-y-1/2 md:bottom-1/3 md:translate-y-1/3 lg:bottom-1/3 lg:translate-y-1/3 flex justify-between items-center max-w-2xl mx-auto gap-x-4">
            <button 
              onClick={prev} 
              className="relative z-[110] flex-shrink-0 w-10 h-10 md:w-12 md:h-12 lg:w-15 lg:h-15 bg-white/30 backdrop-blur-md rounded-full shadow-md flex items-center justify-center text-gray-400 transition-transform active:scale-90 hover:scale-105"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <button 
              onClick={next} 
              className="relative z-[110] flex-shrink-0 w-10 h-10 md:w-12 md:h-12 lg:w-15 lg:h-15 bg-white/30 backdrop-blur-md rounded-full shadow-md flex items-center justify-center text-gray-400 transition-transform active:scale-90 hover:scale-105"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
