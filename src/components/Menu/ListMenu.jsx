import ProductCard from './ProductCard';
import {CakeIcon,SandwichIcon,BurgerIcon,PizzaIcon} from '../../assets/svg';

const MenuSection = () => {


  const categories = [
    { name: 'Cakes & Pastries', icon: <CakeIcon /> },
    { name: 'Sandwich', icon: <SandwichIcon/> },
    { name: 'Burger', icon: <BurgerIcon/> },
    { name: 'Pizza', icon: <PizzaIcon/> },
  ];

  const items = [
    {
      title: 'Chicken Pizza',
      img: '/carousel/carousel-pizza_1-small.png',
      price: 'Rs. 149',
      description: '"Bite into supreme delight of Black Olives, Onions, Grilled Mushrooms, Pepper BBQ Chicken, Peri-Peri Chicken"'
    },
    {
      title: 'Pasta',
      img: '/carousel/carousel-pizza_2-small.png',
      price: 'Rs. 149',
      description: '"Penne pasta in tomato sauce with skinless chicken, pea sprouts, balsamic vinegar, red pepper"'
    },
    {
      title: 'Fruity Chocolate Cake',
      img: '/carousel/carousel-pizza_3-small.png',
      price: 'Rs. 149',
      description: '"Chocolate fudge buttercream swirls, fresh raspberries & strawberries, apricot preserves, mint leaves"'
    },
  ];

  return (
    <>


      {/* Main Title */}
      <h2 className="text-4xl font-bold text-center mb-12 text-gray-800 font-playfair " >Eat Fresh & Healthy</h2>

      {/* Category Navigation */}
      <div className="flex justify-center mb-8 md:mb-16 px-4">
        <div className="bg-white rounded-full px-4 py-3 md:px-8 md:py-4 shadow-lg border border-gray-100 w-full max-w-4xl overflow-x-auto">
          <div className="flex gap-4 md:gap-8 text-sm w-max mx-auto">
            {categories.map((cat) => (
              <button
                key={cat.name}
                className="flex flex-col md:flex-row items-center gap-1 md:gap-2 px-2 py-1 md:px-4 rounded-full hover:bg-gray-50 transition-colors min-w-max"
              >
                <span className="w-8 h-8 md:w-10 md:h-10 flex items-center justify-center">
                  {cat.icon ? cat.icon : cat.emoji}
                </span>
                <span className="hidden md:block text-gray-700 font-medium text-lg md:text-2xl font-poppins">
                  {cat.name}
                </span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Food Items Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 max-w-6xl mx-auto px-4 sm:px-6">
        {items.map((item) => (
          <ProductCard key={item.title} {...item} />
        ))}
      </div>
      </>
  );
};
export default MenuSection;