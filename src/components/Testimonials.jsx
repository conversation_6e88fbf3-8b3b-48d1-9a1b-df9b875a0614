import { useLayoutEffect, useRef, useState, useEffect } from 'react'

const Testimonials = () => {
  const containerRef = useRef(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [cardWidth, setCardWidth] = useState(366) // 350px + 16px gap
  const [visibleCards, setVisibleCards] = useState(1)

  const reviews = [
    {
      name: '<PERSON>',
      img: '/hero/comment1.webp',
      quote: 'The best pizza in town! The crust is perfectly crispy and the toppings are always fresh. My family orders from here every Friday night.',
      rating: 5
    },
    {
      name: '<PERSON>',
      img: '/hero/comment2.webp',
      quote: 'Amazing authentic Italian flavors! The Margherita pizza reminds me of my trip to Naples. Excellent service and fast delivery too.',
      rating: 5
    },
    {
      name: '<PERSON>',
      img: '/hero/comment1.webp',
      quote: 'Love their veggie supreme pizza! As a vegetarian, I appreciate the variety of fresh vegetables and the quality cheese they use.',
      rating: 5
    },
    {
      name: '<PERSON>',
      img: '/hero/comment2.webp',
      quote: 'Amazing authentic Italian flavors! The Margherita pizza reminds me of my trip to Naples. Excellent service and fast delivery too.',
      rating: 5
    },
    {
      name: '<PERSON>',
      img: '/hero/comment1.webp',
      quote: 'Love their veggie supreme pizza! As a vegetarian, I appreciate the variety of fresh vegetables and the quality cheese they use.',
      rating: 5
    },
    {
      name: 'David Wilson',
      img: '/hero/comment2.webp',
      quote: 'Great variety of pizzas and amazing flavors. The delivery was quick and the food was still hot!',
      rating: 5
    },
    {
      name: 'Lisa Brown',
      img: '/hero/comment1.webp',
      quote: 'Perfect pizza every time! Love the crispy crust and generous toppings. My family\'s favorite!',
      rating: 4
    },
    {
      name: 'Robert Taylor',
      img: '/hero/comment2.webp',
      quote: 'Exceptional service and delicious food. The staff is friendly and the atmosphere is great.',
      rating: 5
    },
    {
      name: 'Lisa Brown',
      img: '/hero/comment1.webp',
      quote: 'Perfect pizza every time! Love the crispy crust and generous toppings. My family\'s favorite!',
      rating: 4
    },
    {
      name: 'Robert Taylor',
      img: '/hero/comment2.webp',
      quote: 'Exceptional service and delicious food. The staff is friendly and the atmosphere is great.',
      rating: 5
    }
  ]

  // Handle responsive card sizing
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth
      
      if (width >= 1200) {
        // Desktop large - 3 cards visible
        setCardWidth(366) // 350px + 16px gap
        setVisibleCards(3)
      } else if (width >= 768) {
        // Tablet - 2 cards visible
        setCardWidth(Math.min(366, (width - 64) / 2)) // Max 366px or fit 2 cards
        setVisibleCards(2)
      } else if (width >= 480) {
        // Mobile large - 1 card visible, smaller size
        setCardWidth(Math.min(320, width - 40)) // Max 320px or fit with padding
        setVisibleCards(1)
      } else {
        // Mobile small - 1 card visible, very small
        setCardWidth(Math.min(280, width - 32)) // Max 280px or fit with minimal padding
        setVisibleCards(1)
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Infinite scroll animation
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => prev + 1)
    }, 3000) // Move every 3 seconds

    return () => clearInterval(interval)
  }, [])

  // Reset position when reaching the end for seamless loop
  useEffect(() => {
    if (currentIndex >= reviews.length) {
      setTimeout(() => {
        setCurrentIndex(0)
      }, 1000) // Wait for transition to complete
    }
  }, [currentIndex, reviews.length])

  useLayoutEffect(() => {
    // Simple opacity animation instead of GSAP
    const reviews = containerRef.current?.querySelectorAll('.review')
    if (reviews) {
      reviews.forEach((review, index) => {
        review.style.opacity = '0'
        review.style.transform = 'translateY(40px)'
        setTimeout(() => {
          review.style.transition = 'all 1s ease-out'
          review.style.opacity = '1'
          review.style.transform = 'translateY(0)'
        }, index * 300)
      })
    }
  }, [])

  // Star rating component
  const StarRating = ({ rating }) => {
    return (
      <div className="absolute top-4 right-6 flex justify-center">
        {[...Array(5)].map((_, i) => (
          <svg
            key={i}
            className={`w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    )
  }

  return (
    <section ref={containerRef} className="py-8 sm:py-12 md:py-16 bg-gray-50 px-4 sm:px-6 lg:px-8">
      <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-center mb-8 sm:mb-12 md:mb-16 text-gray-800">
        What Our Customers<br />
        Say About Us
      </h2>

      {/* Testimonials Carousel Container */}
      <div className="relative overflow-hidden pt-16 sm:pt-20 md:pt-25 pb-20">
        <div
          className="testimonials-track flex gap-4 sm:gap-8 md:gap-16 transition-transform duration-1000 ease-linear"
          style={{
            transform: `translateX(-${currentIndex * cardWidth}px)`,
            width: `${(reviews.length * 2) * cardWidth}px` // Double width for seamless loop
          }}
        >
          {/* First set of reviews */}
          {reviews.map((r, index) => (
            <div
              key={`first-${index}`}
              className="relative review text-center transform transition-all duration-300 hover:scale-105 flex-shrink-0"
              style={{ 
                opacity: 1,
                width: `${cardWidth - 16}px`, // Subtract gap
                height: 'auto'
              }}
            >
              {/* Profile Image - positioned above the card */}
              <div
                className="absolute -top-10 sm:-top-10p md:-top-10px left-[30px] w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 rounded-full overflow-hidden z-10"
                style={{
                  boxShadow: 'inset 0 2px 8px rgba(0, 0, 0, 0.1), 0 4px 12px rgba(0, 0, 0, 0.15)'
                }}
              >
                <img
                  className="w-full h-full object-cover"
                  src={r.img}
                  alt={r.name}
                  onError={(e) => {
                    e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(r.name)}&background=random`
                  }}
                />
              </div>

              {/* Card Content */}
              <div
                className="relative bg-white p-4 sm:p-6 md:p-8 pt-8 sm:pt-10 md:pt-12 rounded-2xl sm:rounded-3xl md:rounded-4xl min-h-[250px] sm:min-h-[280px] md:min-h-[300px]"
                style={{
                  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.05)',
                  background: 'linear-gradient(145deg, #ffffff, #f8f9fa)',
                  marginTop: '-2rem sm:-2.5rem md:-3rem', // Pull card up to overlap with image
                  width: `${cardWidth - 16}px`
                }}
              >
                <StarRating rating={r.rating} />

                <h4 className="font-bold text-sm sm:text-base md:text-lg text-gray-800 mb-2 sm:mb-3 md:mb-4 mt-6 sm:mt-7 md:mt-8 px-2">
                  {r.name}
                </h4>
                <p className="px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-lg text-gray-500 leading-relaxed italic line-clamp-4 sm:line-clamp-5 md:line-clamp-none">
                  "{r.quote}"
                </p>
              </div>
            </div>
          ))}

          {/* Second set of reviews for seamless loop */}
          {reviews.map((r, index) => (
            <div
              key={`first-${index}`}
              className="relative review text-center transform transition-all duration-300 hover:scale-105 flex-shrink-0"
              style={{ 
                opacity: 1,
                width: `${cardWidth - 16}px`, // Subtract gap
                height: 'auto'
              }}
            >
              {/* Profile Image - positioned above the card */}
              <div
                className="absolute -top-10 sm:-top-10p md:-top-10px left-[30px] w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 rounded-full overflow-hidden z-10"
                style={{
                  boxShadow: 'inset 0 2px 8px rgba(0, 0, 0, 0.1), 0 4px 12px rgba(0, 0, 0, 0.15)'
                }}
              >
                <img
                  className="w-full h-full object-cover"
                  src={r.img}
                  alt={r.name}
                  onError={(e) => {
                    e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(r.name)}&background=random`
                  }}
                />
              </div>

              {/* Card Content */}
              <div
                className="relative bg-white p-4 sm:p-6 md:p-8 pt-8 sm:pt-10 md:pt-12 rounded-2xl sm:rounded-3xl md:rounded-4xl min-h-[250px] sm:min-h-[280px] md:min-h-[300px]"
                style={{
                  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.05)',
                  background: 'linear-gradient(145deg, #ffffff, #f8f9fa)',
                  marginTop: '-2rem sm:-2.5rem md:-3rem', // Pull card up to overlap with image
                  width: `${cardWidth - 16}px`
                }}
              >
                <StarRating rating={r.rating} />

                <h4 className="font-bold text-sm sm:text-base md:text-lg text-gray-800 mb-2 sm:mb-3 md:mb-4 mt-6 sm:mt-7 md:mt-8 px-2">
                  {r.name}
                </h4>
                <p className="px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-lg text-gray-500 leading-relaxed italic line-clamp-4 sm:line-clamp-5 md:line-clamp-none">
                  "{r.quote}"
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Optional: Pause on hover overlay */}
        <div
          className="absolute inset-0 z-10 pointer-events-none"
          style={{ background: 'transparent' }}
        />
      </div>

      {/* Add custom CSS for line-clamp */}
      <style jsx="true">{`
        .line-clamp-4 {
          display: -webkit-box;
          -webkit-line-clamp: 4;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        .line-clamp-5 {
          display: -webkit-box;
          -webkit-line-clamp: 5;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        @media (max-width: 640px) {
          .line-clamp-none {
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
      `}</style>
    </section>
  )
}

export default Testimonials