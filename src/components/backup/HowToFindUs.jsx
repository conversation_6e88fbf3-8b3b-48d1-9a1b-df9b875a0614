import React from "react";

const ContactSection = () => {
  return (
    <section className="bg-gray-50 py-8 md:py-12 px-4 sm:px-6 lg:px-8 w-full lg:w-4/5 xl:w-4/6 mx-auto text-center">
      {/* Judul */}
      <h2 className="text-center text-xl sm:text-2xl font-bold mb-6 md:mb-8">HOW TO FIND US</h2>

      {/* Info dan cabang */}
      <div className="flex flex-col lg:flex-row gap-8 lg:gap-10 xl:gap-40 items-start justify-center">
        {/* Kiri: Info toko */}
        <div className="w-full lg:w-auto">
          <h3 className="text-base sm:text-lg font-semibold mb-2">Shop timings</h3>
          <p className="mb-4 sm:mb-6">Mon - Sun : 9am - 10pm</p>

          <h3 className="text-base sm:text-lg font-semibold mb-2">Production Unit</h3>
          <p className="text-sm sm:text-base">
            NO 7 M.P.A Church Road, Vijaya Nursery Garden, <br className="hidden sm:block" />
            Thirumullaivoyal, Chennai-62
          </p>
        </div>

        {/* Kanan: Cabang */}
        <div className="w-full lg:w-auto">
          <h3 className="text-base sm:text-lg font-semibold mb-2">Our Branches</h3>
          <div className="bg-white rounded-2xl p-4 sm:p-6 w-full max-w-[300px] mx-auto lg:mx-0">
            <select className="bg-gray-100 py-2 sm:py-[10px] px-4 sm:px-5 block w-full border-0 rounded-full mb-3 sm:mb-4 shadow-[0_5px_6px_rgba(0,0,0,0.15)] text-sm sm:text-base text-center">
              <option>AMBATTUR</option>
              {/* Tambahkan opsi lain jika perlu */}
            </select>
            <p className="mb-1 sm:mb-2 text-sm sm:text-base">NO 18, Ayyapakkam Main Road,</p>
            <p className="mb-1 sm:mb-2 text-sm sm:text-base">West Ambattur, Chennai-32</p>
            <p className="text-sm sm:text-base">Contact: +91-9176677594</p>
          </div>
        </div>
      </div>

      {/* Map */}
      <div className="mt-8 sm:mt-12 px-2 sm:px-0">
        <div className="relative pb-[75%] sm:pb-[50%] lg:pb-[40%] w-full overflow-hidden rounded-2xl sm:rounded-[50px] shadow-md">
          <iframe
            title="La Bakery Ambattur Map"
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d15813.645588482514!2d110.38101908715822!3d-7.7461135000000025!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e7a5975f3c9e3e1%3A0xb353c4497a657f89!2sKy%27s%20Cakery!5e0!3m2!1sid!2sid!4v1752867816040!5m2!1sid!2sid"
            className="absolute top-0 left-0 w-full h-full border-0"
            allowFullScreen=""
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
          ></iframe>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
