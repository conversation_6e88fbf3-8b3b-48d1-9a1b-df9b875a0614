import { useLayoutEffect, useRef } from 'react';
import gsap from 'gsap';

const HeroSection = () => {
  const root = useRef(null);

  useLayoutEffect(() => {
    const ctx = gsap.context(() => {
      // The timeline will be created here
      const tl = gsap.timeline();

      // Use .from() to animate from a starting state to the default state in the CSS
      // 1. Leaf
      tl.from('.leaf', {
        scale: 0.5,
        opacity: 0,
        duration: 0.8,
        ease: 'back.out(1.7)',
      });

      // 2. Main Hero and Sauce
      tl.from('.main-hero', {
        scale: 0.2,
        rotate: -45,
        opacity: 0,
        duration: 1,
        transformOrigin: 'center center',
        ease: 'elastic.out(1, 0.75)',
      }, '-=0.5'); // Overlap with previous animation

      tl.from('.sauce', {
        scale: 0,
        opacity: 0,
        duration: 0.6,
        ease: 'back.out(1.7)',
      }, '-=0.6'); // Overlap with previous animation

      // 3. Background Hero
      tl.from('.bg-hero', {
        opacity: 0,
        duration: 1,
      }, '<'); // Start at the same time as the previous animation

      // 4. Grilled Chicken, Fish & Vegetables 1 & 2
      tl.from('.grilled-chicken', {
        x: -100,
        opacity: 0,
        duration: 0.8,
        ease: 'back.out(1.7)',
      }, '-=0.2');

      tl.from('.fish-veg-1', {
        y: 100,
        opacity: 0,
        duration: 0.8,
        ease: 'back.out(1.7)',
      }, '<0.2'); // Start 0.2s after the previous one starts

      tl.from('.fish-veg-2', {
        x: 100,
        opacity: 0,
        duration: 0.8,
        ease: 'back.out(1.7)',
      }, '<0.2'); // Start 0.2s after the previous one starts

      // 5. Italian Pizza and Comments
      tl.from(['.italian-pizza', '.comment1', '.comment2'], {
        y: 10,
        opacity: 0,
        duration: 0.7,
        ease: 'back.out(1.7)',
        stagger: 0.2,
      }, '-=0.5');

      // 6. Yoyo animation for Italian Pizza and Comments
      gsap.to(['.italian-pizza', '.comment1', '.comment2'], {
        y: '0px',
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        stagger: {
          each: 0.1,
          from: 'random',
        },
      });
    }, root);

    // Cleanup function
    return () => ctx.revert();
  }, []);

  return (
    <section ref={root} className="relative w-[100vw] max-w-[1500px] mx-auto flex flex-col-reverse md:flex-row justify-around gap-8 px-8 xl:py-16 md:py-8 bg-gray-50">
      <div className="hero-item w-[100%] md:w-[50%] flex-1 pt-15 z-11 pb-[50%] h-0 -mt-[15%] md:-mt-0">
        <h1 className="font-playfair text-[7vw] md:text-[4.5vw] lg:text-[5vw] 2xl:text-[5rem] font-bold mb-4 text-center sm:text-left">
         It's not just a Food
         <br />
         It's a Experience!
        </h1>
        <h1 className="text-[4rem] font-bold mb-4">
         
        </h1>
        <p className="font-poppins text-[3.5vw] md:text-[2vw] 2xl:text-[1.5rem] mt-6 mb-5 md:mb-20 text-gray-600 text-center sm:text-left ">
          This is Lorem ipsum dolor sit amet, consectetur adipisicing elit. Numquam eius vel tempore consectetur nesciunt? Nam eius tenetur recusandae optio aperiam.
        </p>
        <div className="flex gap-4 w-[120%]">
          <button className=" bg-rose-500 text-white hover:bg-rose-600 flex flex-row justify-center items-center gap-3 px-4 md:px-5 lg:px-6 py-3 rounded-full border border-rose-500 shadow-[0_10px_10px_rgba(0,0,0,0.3)] cursor-pointer transition-all duration-200">
            <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.002 0.5C5.38395 0.5 0 5.883 0 12.5C0 19.117 5.383 24.5 12 24.5C13.788 24.5 15.476 24.0898 17 23.3848V21.1328C15.528 21.9928 13.826 22.5 12 22.5C6.486 22.5 2 18.014 2 12.5C2 6.986 6.486 2.5 12 2.5C13.041 2.5 14.0375 2.67661 14.9805 2.97461C15.2975 2.34461 15.6877 1.76567 16.1387 1.26367C14.8467 0.784672 13.46 0.5 12.002 0.5ZM20 1.41211C18.677 1.41211 17.6749 2.33285 17.0254 3.39844C16.3759 4.46402 16 5.77292 16 7.08008C16 8.38723 16.4153 9.50697 17.1621 10.2734C17.6657 10.7902 18.3164 11.063 19 11.2285V23.5H21V11.2285C21.6836 11.063 22.3343 10.7902 22.8379 10.2734C23.5847 9.50697 24 8.38723 24 7.08008C24 5.77292 23.6241 4.46402 22.9746 3.39844C22.3251 2.33285 21.323 1.41211 20 1.41211ZM20 3.41211C20.334 3.41211 20.8313 3.72379 21.2676 4.43945C21.7038 5.15512 22 6.17823 22 7.08008C22 7.98192 21.7432 8.52917 21.4043 8.87695C21.0654 9.22474 20.6024 9.41211 20 9.41211C19.3976 9.41211 18.9346 9.22474 18.5957 8.87695C18.2568 8.52917 18 7.98192 18 7.08008C18 6.17823 18.2962 5.15512 18.7324 4.43945C19.1687 3.72379 19.666 3.41211 20 3.41211ZM12 4.5C7.589 4.5 4 8.089 4 12.5C4 16.911 7.589 20.5 12 20.5V18.5C8.691 18.5 6 15.809 6 12.5C6 9.191 8.691 6.5 12 6.5C12.706 6.5 13.3808 6.62952 14.0098 6.85352C14.0278 6.17852 14.1211 5.49889 14.2891 4.83789C13.5631 4.62089 12.796 4.5 12 4.5Z" fill="#231D41"/>
</svg>

            <span className="font-montserrat">Our Menu</span>
          </button>
          <button className="bg-white flex flex-row justify-center items-center gap-3 px-6 py-3 rounded-full border border-gray-200 hover:bg-gray-200  shadow-[0_10px_10px_rgba(0,0,0,0.3)] cursor-pointer transition-all duration-200">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 0.5H2C0.9 0.5 0 1.4 0 2.5V20.5L4 16.5H18C19.1 16.5 20 15.6 20 14.5V2.5C20 1.4 19.1 0.5 18 0.5ZM18 14.5H4L2 16.5V2.5H18V14.5Z" fill="black"/>
            </svg>
            <span className="font-montserrat">About Us</span>
          </button>
        </div>
      </div>
      <div className="w-[100%] md:w-[50%] relative hero-item pb-[100%] md:pb-[50%] md:h-0">
         <img
          className="leaf absolute z-0 rounded-lg top-0 right-[10%] brightness-110 w-[50%] h-auto"
          src="/hero/bg2-main-hero.webp"
          alt="leaf"
        />       
        <img
          className="bg-hero absolute z-1 rounded-lg opacity-50 w-[100%] h-auto top-[10px] left-[0%]"
          src="/hero/bg-main-hero.webp"
          alt="Background Hero"
        />
        <img
          className="main-hero absolute z-10 rounded-lg w-[80%] h-auto top-[12%] left-[15%]"
          src="/hero/main-hero.webp"
          alt="Main Hero"
        />
                <img
          className="grilled-chicken absolute z-10 w-[20%] h-auto -left-[4%] top-[61%]"
          src="/hero/grilled-chicken.webp"
          alt="grilled-chicken"
        />
 
               <img
          className="fish-veg-1 absolute z-10 w-[20%] h-auto left-[40%] top-[90%]"
          src="/hero/fish-vegetables.webp"
          alt="fish and vegetables"
        />
                        <img
          className="fish-veg-2 absolute z-10 w-[20%] h-auto left-[83%] top-[67%]"
          src="/hero/fish-vegetables2.webp"
          alt="fish and vegetables 2"
        />

<img
          className="sauce absolute z-10 w-[15%] h-auto left-[35%] top-[76%]"
          src="/hero/sauce.webp"
          alt="sauce"
        />
                                        <img
          className="italian-pizza absolute z-10 w-[30%] h-auto left-[0%] top-[40%]"
          src="/hero/italian-pizza.webp"
          alt="italian pizza"
        />




                                                <img
          className="comment2 absolute z-10 w-[25%] h-auto left-[65%] top-[37%]"
          src="/hero/comment2.webp"
          alt="comment2"
        />
                                        <img
          className="comment1 absolute z-10 w-[25%] h-auto left-[18%] top-[75%]"
          src="/hero/comment1.webp"
          alt="comment1"
        />
      </div>
      <img 
        className="z-0 absolute -bottom-[100px] left-[-15%]  w-[25%] drop-shadow-[0_20px_20px_rgba(88,195,18,0.74)]"
        src="/hero/big-leaf.webp"
        alt="big-leaf"
      />
            <img
        className="z-0 absolute top-[33%] sm:top-[26%] md:top-[29%] left-[3%] sm:left-[1%] md:-left-[1%] xl2:-left-[3%]  w-[6%] drop-shadow-[0_20px_20px_rgba(88,195,18,0.74)]"
        src="/hero/leaf2.webp"
        alt="leaf2"
      />
            <img
        className="z-0 absolute top-[90%] left-[6%]  w-[5%] drop-shadow-[0_20px_20px_rgba(88,195,18,0.74)]"
        src="/hero/leaf3.webp"
        alt="leaf3"
      />

    </section>
  );
};
export default HeroSection;