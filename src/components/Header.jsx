import { useState, useEffect } from 'react';

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('home');

  // Handle scroll to update active section
  useEffect(() => {
    const handleScroll = () => {
      const sections = ['home', 'menu', 'about', 'gallery', 'contact'];
      const scrollPosition = window.scrollY + 100; // Offset untuk kompensasi header

      sections.forEach(section => {
        const element = document.getElementById(section);
        if (element) {
          const offsetTop = element.offsetTop;
          const offsetHeight = element.offsetHeight;
          
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(section);
          }
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle smooth scroll to section
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerHeight = 80; // Approximate header height
      const offsetTop = element.offsetTop - headerHeight;
      
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
    setIsMobileMenuOpen(false); // Close mobile menu after click
  };

  // Navigation items
  const navItems = [
    { id: 'home', label: 'Home' },
    { id: 'menu', label: 'Menu' },
    { id: 'about', label: 'About US' },
    { id: 'gallery', label: 'Gallery' },
    { id: 'contact', label: 'Contact' }
  ];

  // Active button style
  const getButtonStyle = (isActive) => ({
    background: isActive ? '#F1F3F6' : 'transparent',
    boxShadow: isActive 
      ? 'rgb(197 192 192) 4px 5px 8px inset, inset rgb(255 255 255) -6px -6px 5px'
      : 'none'
  });

  return (
    <header 
      className="bg-[#F1F3F6] px-[10vw] lg:px-[10vw] md:px-8 sm:px-6 py-3 flex justify-between items-center sticky top-0 z-50"
      style={{ boxShadow: '0 4px 10px -8px rgba(0, 0, 0, 0.1)' }}
    >
      {/* Logo */}
      <div className="flex items-center">
        <img src="/public/logo.webp" alt="Logo" className="h-10" />
      </div>

      {/* Desktop Navigation */}
      <nav className="hidden lg:flex items-center gap-4 text-base font-semibold">
        {navItems.map((item) => (
          <button
            key={item.id}
            onClick={() => scrollToSection(item.id)}
            className={`${
              activeSection === item.id 
                ? 'text-orange-600' 
                : 'text-gray-700 hover:text-orange-600'
            } transition-colors duration-300 px-4 py-1 rounded-xl cursor-pointer`}
            style={getButtonStyle(activeSection === item.id)}
          >
            {item.label}
          </button>
        ))}
      </nav>

      {/* Desktop Search and User */}
      <div className="hidden lg:flex items-center gap-6">
        {/* Search Bar */}
        <div className="relative flex items-center">
          <input
            type="search"
            placeholder="Search Here..."
            className="pl-5 pr-10 py-3 rounded-full bg-[#F1F3F6] text-sm text-gray-700 placeholder-gray-500 focus:outline-none w-52"
            style={{
              boxShadow: 'inset 5px 5px 8px #d9dbde, inset -5px -5px 8px #ffffff'
            }}
          />
          <svg
            className="w-4 h-4 text-orange-500 absolute right-4 top-1/2 transform -translate-y-1/2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            strokeWidth={2.5}
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>

        {/* User Profile Icon */}
        <div 
          className="w-9 h-9 bg-[#F1F3F6] rounded-[8px] flex items-center justify-center cursor-pointer"
          style={{
            boxShadow: 'rgb(197 192 192) 4px 5px 8px inset, inset rgb(255 255 255) -6px -6px 5px'
          }}
        >
          <svg width="17" height="21" viewBox="0 0 17 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0.80957 13.7645C0.80957 12.958 1.30432 12.2764 1.96313 12.1874C6.90743 11.5206 10.115 11.5807 15.0484 12.2022C15.2947 12.2337 15.5282 12.352 15.7203 12.5425C15.9124 12.733 16.0548 12.9876 16.1302 13.2751C16.2055 13.5626 16.2104 13.8706 16.1444 14.1614C16.0784 14.4523 15.9443 14.7135 15.7585 14.913C9.93618 21.0958 6.60879 21.0107 1.21973 14.9192C0.95697 14.6226 0.80957 14.2002 0.80957 13.7645Z" fill="#FF8243"/>
            <path fillRule="evenodd" clipRule="evenodd" d="M15.1127 13.5887C10.1196 13.0233 6.89439 12.9704 1.90193 13.5751C1.73759 13.5961 1.58609 13.6827 1.47643 13.8183C1.36677 13.9539 1.30666 14.129 1.30762 14.3101C1.30762 14.5148 1.38542 14.7088 1.51683 14.8433C4.2419 17.6102 6.30662 18.8605 8.32491 18.8683C10.3504 18.8762 12.5263 17.6359 15.4749 14.8233C15.5686 14.732 15.636 14.6129 15.6691 14.4805C15.7021 14.3481 15.6992 14.208 15.6608 14.0773C15.6225 13.9466 15.5502 13.8309 15.4529 13.7443C15.3556 13.6577 15.2374 13.6032 15.1127 13.5887ZM1.75874 12.1524C6.85581 11.5347 10.1758 11.5905 15.2481 12.1652C15.6262 12.2084 15.9846 12.3714 16.2794 12.6341C16.5742 12.8968 16.7926 13.248 16.9079 13.6445C17.0232 14.041 17.0304 14.4657 16.9286 14.8667C16.8268 15.2676 16.6204 15.6273 16.3347 15.9018C13.3435 18.756 10.8525 20.3104 8.32099 20.2997C5.78226 20.2897 3.4011 18.7087 0.628312 15.8925C0.429785 15.6901 0.271524 15.4451 0.163418 15.1727C0.0553113 14.9003 -0.00031875 14.6064 3.7916e-06 14.3094C-0.000949729 13.7758 0.177972 13.2606 0.502496 12.8625C0.827019 12.4643 1.27433 12.2119 1.75874 12.1524Z" fill="#FF8243"/>
            <path d="M13.699 5.04466C13.699 6.38259 13.148 7.66572 12.1671 8.61177C11.1862 9.55783 9.85578 10.0893 8.46858 10.0893C7.08138 10.0893 5.751 9.55783 4.7701 8.61177C3.7892 7.66572 3.23813 6.38259 3.23813 5.04466C3.23813 3.70673 3.7892 2.4236 4.7701 1.47755C5.751 0.531489 7.08138 0 8.46858 0C9.85578 0 11.1862 0.531489 12.1671 1.47755C13.148 2.4236 13.699 3.70673 13.699 5.04466Z" fill="#FF8243"/>
          </svg>
        </div>
      </div>

      {/* Mobile Menu Button */}
      <div className="lg:hidden flex items-center gap-4">
        {/* Mobile Search Icon */}
        <div 
          className="w-9 h-9 bg-[#F1F3F6] rounded-[8px] flex items-center justify-center cursor-pointer"
          style={{
            boxShadow: 'rgb(197 192 192) 4px 5px 8px inset, inset rgb(255 255 255) -6px -6px 5px'
          }}
        >
          <svg
            className="w-4 h-4 text-orange-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            strokeWidth={2.5}
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>

        {/* Mobile User Icon */}
        <div 
          className="w-9 h-9 bg-[#F1F3F6] rounded-[8px] flex items-center justify-center cursor-pointer"
          style={{
            boxShadow: 'rgb(197 192 192) 4px 5px 8px inset, inset rgb(255 255 255) -6px -6px 5px'
          }}
        >
          <svg width="17" height="21" viewBox="0 0 17 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0.80957 13.7645C0.80957 12.958 1.30432 12.2764 1.96313 12.1874C6.90743 11.5206 10.115 11.5807 15.0484 12.2022C15.2947 12.2337 15.5282 12.352 15.7203 12.5425C15.9124 12.733 16.0548 12.9876 16.1302 13.2751C16.2055 13.5626 16.2104 13.8706 16.1444 14.1614C16.0784 14.4523 15.9443 14.7135 15.7585 14.913C9.93618 21.0958 6.60879 21.0107 1.21973 14.9192C0.95697 14.6226 0.80957 14.2002 0.80957 13.7645Z" fill="#FF8243"/>
            <path fillRule="evenodd" clipRule="evenodd" d="M15.1127 13.5887C10.1196 13.0233 6.89439 12.9704 1.90193 13.5751C1.73759 13.5961 1.58609 13.6827 1.47643 13.8183C1.36677 13.9539 1.30666 14.129 1.30762 14.3101C1.30762 14.5148 1.38542 14.7088 1.51683 14.8433C4.2419 17.6102 6.30662 18.8605 8.32491 18.8683C10.3504 18.8762 12.5263 17.6359 15.4749 14.8233C15.5686 14.732 15.636 14.6129 15.6691 14.4805C15.7021 14.3481 15.6992 14.208 15.6608 14.0773C15.6225 13.9466 15.5502 13.8309 15.4529 13.7443C15.3556 13.6577 15.2374 13.6032 15.1127 13.5887ZM1.75874 12.1524C6.85581 11.5347 10.1758 11.5905 15.2481 12.1652C15.6262 12.2084 15.9846 12.3714 16.2794 12.6341C16.5742 12.8968 16.7926 13.248 16.9079 13.6445C17.0232 14.041 17.0304 14.4657 16.9286 14.8667C16.8268 15.2676 16.6204 15.6273 16.3347 15.9018C13.3435 18.756 10.8525 20.3104 8.32099 20.2997C5.78226 20.2897 3.4011 18.7087 0.628312 15.8925C0.429785 15.6901 0.271524 15.4451 0.163418 15.1727C0.0553113 14.9003 -0.00031875 14.6064 3.7916e-06 14.3094C-0.000949729 13.7758 0.177972 13.2606 0.502496 12.8625C0.827019 12.4643 1.27433 12.2119 1.75874 12.1524Z" fill="#FF8243"/>
            <path d="M13.699 5.04466C13.699 6.38259 13.148 7.66572 12.1671 8.61177C11.1862 9.55783 9.85578 10.0893 8.46858 10.0893C7.08138 10.0893 5.751 9.55783 4.7701 8.61177C3.7892 7.66572 3.23813 6.38259 3.23813 5.04466C3.23813 3.70673 3.7892 2.4236 4.7701 1.47755C5.751 0.531489 7.08138 0 8.46858 0C9.85578 0 11.1862 0.531489 12.1671 1.47755C13.148 2.4236 13.699 3.70673 13.699 5.04466Z" fill="#FF8243"/>
          </svg>
        </div>

        {/* Hamburger Menu */}
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="w-9 h-9 bg-[#F1F3F6] rounded-[8px] flex items-center justify-center cursor-pointer"
          style={{
            boxShadow: 'rgb(197 192 192) 4px 5px 8px inset, inset rgb(255 255 255) -6px -6px 5px'
          }}
        >
          <div className="w-5 h-5 flex flex-col justify-center items-center">
            <span className={`bg-orange-500 block transition-all duration-300 ease-out h-0.5 w-4 rounded-sm ${isMobileMenuOpen ? 'rotate-45 translate-y-1' : '-translate-y-0.5'}`}></span>
            <span className={`bg-orange-500 block transition-all duration-300 ease-out h-0.5 w-4 rounded-sm my-0.5 ${isMobileMenuOpen ? 'opacity-0' : 'opacity-100'}`}></span>
            <span className={`bg-orange-500 block transition-all duration-300 ease-out h-0.5 w-4 rounded-sm ${isMobileMenuOpen ? '-rotate-45 -translate-y-1' : 'translate-y-0.5'}`}></span>
          </div>
        </button>
      </div>

      {/* Mobile Menu Dropdown */}
      <div className={`lg:hidden absolute top-full left-0 right-0 bg-[#F1F3F6] transition-all duration-300 ease-in-out ${
        isMobileMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 overflow-hidden'
      }`}
      style={{ boxShadow: '0 4px 10px -8px rgba(0, 0, 0, 0.1)' }}>
        <nav className="flex flex-col p-4 gap-2">
          {navItems.map((item) => (
            <button
              key={item.id}
              onClick={() => scrollToSection(item.id)}
              className={`${
                activeSection === item.id 
                  ? 'text-orange-600' 
                  : 'text-gray-700'
              } text-left px-4 py-3 rounded-xl font-semibold transition-colors duration-300`}
              style={getButtonStyle(activeSection === item.id)}
            >
              {item.label}
            </button>
          ))}
          
          {/* Mobile Search Bar */}
          <div className="relative flex items-center mt-2">
            <input
              type="search"
              placeholder="Search Here..."
              className="pl-5 pr-10 py-3 rounded-full bg-[#F1F3F6] text-sm text-gray-700 placeholder-gray-500 focus:outline-none w-full"
              style={{
                boxShadow: 'inset 5px 5px 8px #d9dbde, inset -5px -5px 8px #ffffff'
              }}
            />
            <svg
              className="w-4 h-4 text-orange-500 absolute right-4 top-1/2 transform -translate-y-1/2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              strokeWidth={2.5}
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </nav>
      </div>
    </header>
  );
};

export default Header;