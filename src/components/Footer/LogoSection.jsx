import React from 'react';
import { images } from '../../constants/images';

const LogoSection = () => (
  <div className="text-center">
    <img 
      src={images.footer.logo} 
      alt="LaBakery Logo" 
      className="mx-auto w-32 md:w-36 h-auto mb-4" 
    />
    <p className="text-gray-600 text-sm md:text-base max-w-xs mx-auto mb-6 leading-relaxed">
      <PERSON>rem ipsum, dolor sit amet consectetur adipisicing elit. Mollitia, tenetur.
    </p>
    <div className="flex justify-center space-x-4">
      <a 
        href="#" 
        className="w-15 h-15 rounded-full bg-gray-100 flex items-center justify-center hover:bg-primary hover:text-white transition-colors duration-200"
        aria-label="Facebook"
      >
        <img 
          src={images.footer.social.facebook} 
          alt="Facebook" 
          className="w-full h-auto" 
        />
      </a>
      <a 
        href="#" 
        className="w-15 h-15 rounded-full bg-gray-100 flex items-center justify-center hover:bg-primary hover:text-white transition-colors duration-200"
        aria-label="Instagram"
      >
        <img 
          src={images.footer.social.instagram} 
          alt="Instagram" 
          className="w-full h-auto" 
        />
      </a>
      <a 
        href="#" 
        className="w-15 h-15 rounded-full bg-gray-100 flex items-center justify-center hover:bg-primary hover:text-white transition-colors duration-200"
        aria-label="Twitter"
      >
        <img 
          src={images.footer.social.twitter} 
          alt="Twitter" 
          className="w-full h-auto" 
        />
      </a>
    </div>
  </div>
);

export default LogoSection;
