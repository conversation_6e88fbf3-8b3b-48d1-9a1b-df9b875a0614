import React, { useState, useEffect } from 'react';
import LogoSection from './LogoSection';
import LinksSection from './LinksSection';
import ScrollToTop from './ScrollToTop';

const Footer = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [activeSection, setActiveSection] = useState(null);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  const toggleSection = (section) => {
    setActiveSection(activeSection === section ? null : section);
  };

  const locationLinks = [
    { text: 'Ambattur', url: '#' },
    { text: 'Avadi', url: '#' },
    { text: 'Thirumullaivoyal', url: '#' },
    { text: 'Poompozilnagar', url: '#' },
    { text: 'Mugapair', url: '#' },
  ];

  const navLinks = [
    { text: 'Home', url: '#' },
    { text: 'About', url: '#' },
    { text: 'Menu', url: '#' },
    { text: 'Gallery', url: '#' },
    { text: 'Contact', url: '#' },
  ];

  const companyLinks = [
    { text: 'Terms & Conditions', url: '#' },
    { text: 'Privacy Policy', url: '#' },
    { text: 'Cookie Policy', url: '#' },
  ];

  return (
    <footer className="bg-gray-50 text-gray-800 py-16 md:py-20 font-['Poppins']">
      <div className="container mx-auto px-5 md:px-6">
        <div className="flex flex-col md:flex-row flex-wrap justify-between gap-8">
          <div className="w-full md:w-auto">
            <LogoSection />
          </div>
          
          <LinksSection 
            title="Our Locations"
            links={locationLinks}
            isMobile={isMobile}
            isActive={activeSection === 'locations'}
            onClick={() => toggleSection('locations')}
          />
          
          <LinksSection 
            title="Quick Links"
            links={navLinks}
            isMobile={isMobile}
            isActive={activeSection === 'links'}
            onClick={() => toggleSection('links')}
          />
          
          <LinksSection 
            title="Company"
            links={companyLinks}
            isMobile={isMobile}
            isActive={activeSection === 'company'}
            onClick={() => toggleSection('company')}
          />
        </div>
        
        <div className="border-t border-gray-200 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-600 text-sm mb-4 md:mb-0">
            Copyright &copy; {new Date().getFullYear()} Maddy. All Rights Reserved.
          </p>
          <ScrollToTop />
        </div>
      </div>
    </footer>
  );
};

export default Footer;
