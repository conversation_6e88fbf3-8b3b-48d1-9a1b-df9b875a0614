import React from 'react';
import PropTypes from 'prop-types';

const LinksSection = ({ title, links, isMobile = false, isActive = false, onClick }) => {
  const handleClick = (e) => {
    if (isMobile) {
      e.preventDefault();
      if (onClick) {
        onClick();
      }
    }
  };

  return (
    <div className={`w-full md:w-auto ${isMobile ? 'border-b border-gray-200' : ''}`}>
      <h4 
        className={`font-['Playfair_Display'] text-xl font-bold mb-5 cursor-pointer relative 
          ${isMobile ? 'py-4 flex items-center justify-between' : ''}
          ${isActive ? 'text-primary' : 'text-gray-800'}`}
        onClick={handleClick}
      >
        {title}
        {isMobile && (
          <span className="w-6 h-6 flex items-center justify-center bg-gray-100 rounded ml-2">
            {isActive ? '−' : '+'}
          </span>
        )}
      </h4>
      
      <div 
        className={`transition-all duration-300 overflow-hidden ${
          isMobile ? (isActive ? 'max-h-96' : 'max-h-0') : 'max-h-96'
        }`}
      >
        <ul className="space-y-3">
          {links.map((link, index) => (
            <li key={index}>
              <a 
                href={link.url} 
                className="text-gray-600 hover:text-primary text-sm md:text-base transition-colors duration-200"
                onClick={(e) => isMobile && e.stopPropagation()}
              >
                {link.text}
              </a>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

LinksSection.propTypes = {
  title: PropTypes.string.isRequired,
  links: PropTypes.arrayOf(
    PropTypes.shape({
      text: PropTypes.string.isRequired,
      url: PropTypes.string.isRequired,
    })
  ).isRequired,
  isMobile: PropTypes.bool,
  isActive: PropTypes.bool,
  onClick: PropTypes.func,
};

export default LinksSection;
