import React, { useState, useEffect } from 'react';

const ScrollToTop = () => {
  const [isVisible, setIsVisible] = useState(false);

  const toggleVisibility = () => {
    if (window.pageYOffset > 300) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  };

  const scrollToTop = (e) => {
    e.preventDefault();
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  useEffect(() => {
    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  return (
    <a 
      href="#top" 
      className={`fixed bottom-3 right-12 p-3 bg-white rounded-full 
        shadow-[0_4px_12px_rgba(0,0,0,0.15),0_2px_6px_rgba(0,0,0,0.1)]
        hover:shadow-[0_6px_16px_rgba(0,0,0,0.2),0_2px_8px_rgba(0,0,0,0.15)]
        active:shadow-[0_2px_8px_rgba(0,0,0,0.15),0_1px_4px_rgba(0,0,0,0.1)]
        transition-all duration-300 ${
          isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
      onClick={scrollToTop}
      aria-label="Scroll to top"
    >
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        width="24" 
        height="24" 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round"
        className="text-gray-700"
      >
        <path d="m18 15-6-6-6 6"/>
      </svg>
    </a>
  );
};

export default ScrollToTop;