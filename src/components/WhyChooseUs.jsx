import { useLayoutEffect, useRef } from 'react'

const WhyChooseUs = () => {
  const containerRef = useRef(null)

  useLayoutEffect(() => {
    // Simple fade-in animation without GSAP
    const elements = containerRef.current?.querySelectorAll('.animate-in')
    elements?.forEach((el, index) => {
      el.style.opacity = '0'
      el.style.transform = 'translateY(20px)'
      setTimeout(() => {
        el.style.transition = 'all 0.6s ease-out'
        el.style.opacity = '1'
        el.style.transform = 'translateY(0)'
      }, index * 200)
    })
  }, [])

  return (
    <section
      ref={containerRef}
      className="relative py-20 bg-gray-50 overflow-hidden"
    >
      {/* Main heading */}
      <div className="text-center mb-16 animate-in">
        <h2 className="text-4xl font-bold text-gray-900 mb-4">
          Why Choose Us
        </h2>
        <p className="text-gray-600 max-w-md mx-auto text-base leading-relaxed">
          Fresh food is food which has not been preserved and has not spoiled yet. For vegetables and fruits
        </p>
      </div>

      {/* Main content container */}
      <div className="relative max-w-7xl mx-auto px-8 z-40">
        {/* Positioning container for absolute positioned elements */}
        <div className="relative h-[350px] animate-in flex flex-col sm:flex md:block  ">

          {/* Central image */}
          <div className="absolute left-1/2 bottom-[-150px] md:bottom-[-110px] transform -translate-x-1/2 z-20">
            <div className="central-image w-[50vw] max-w-[700px]">
              <img
                src="/why-choose-us/main-image.webp"
                alt="Fresh spices and ingredients"

              />
            </div>
          </div>
          <div className='flex sm:flex sm:flex-row justify-center md:block w-full h-40'>
            {/* ALWAYS FRESH - Top Left */}
            <div className="feature-card md:absolute top-[-50px] left-40 text-center max-w-[200px] animate-in">
              <div className="flex justify-center mb-3">
                <img src="/why-choose-us/small-plan.webp" alt="Always Fresh" className="w-12 h-12" />
              </div>
              <h3 className="font-bold text-sm text-gray-900 mb-2 uppercase">Always Fresh</h3>
              <p className="text-xs text-gray-600 leading-relaxed">
                Fresh food is food which has not been preserved and has not spoiled yet. For vegetables and fruits
              </p>
            </div>

            {/* 100% NATURAL - Top Right */}
            <div className="feature-card md:absolute top-[-50px]  right-40 text-center max-w-[200px] animate-in">
              <div className="flex justify-center mb-3">
                <img src="/why-choose-us/medal.webp" alt="100% Natural" className="w-12 h-12" />
              </div>
              <h3 className="font-bold text-sm text-gray-900 mb-2 uppercase">100% Natural</h3>
              <p className="text-xs text-gray-600 leading-relaxed">
                Fresh food is food which has not been preserved and has not spoiled yet. For vegetables and fruits
              </p>
            </div>
          </div>

          <div className='flex sm:flex sm:flex-row justify-between md:block w-full h-40 pt-10 -mt-5 md:pt-0'>
            {/* SUPER HEALTHY - Bottom Left */}
            <div className="feature-card md:absolute bottom-10 left-10 text-center max-w-[200px] animate-in">
              <div className="flex justify-center mb-3">
                <img src="/why-choose-us/love.webp" alt="Super Healthy" className="w-12 h-12" />
              </div>
              <h3 className="font-bold text-sm text-gray-900 mb-2 uppercase">Super Healthy</h3>
              <p className="text-xs text-gray-600 leading-relaxed">
                Fresh food is food which has not been preserved and has not spoiled yet.
              </p>
            </div>

            {/* PREMIUM QUALITY - Bottom Right */}
            <div className="feature-card md:absolute bottom-10 right-10 text-center max-w-[200px] animate-in">
              <div className="flex justify-center mb-3">
                <img src="/why-choose-us/leaf-small.webp" alt="Premium Quality" className="w-12 h-12" />
              </div>
              <h3 className="font-bold text-sm text-gray-900 mb-2 uppercase">Premium Quality</h3>
              <p className="text-xs text-gray-600 leading-relaxed">
                Fresh food is food which has not been preserved and has not spoiled yet.
              </p>
            </div>
          </div>

          {/* NO CONNECTING LINES - sesuai gambar tidak ada garis */}
        </div>
      </div>

      {/* Franchise section */}
      <div className="franchise-section animate-in mt-[30px] md:mt-0 p-5">
        <div
          className="relative rounded-3xl p-10 md:p-12 m-10 max-w-5xl mx-auto text-center overflow-hidden"
          style={{
            backgroundImage: 'url(/why-choose-us/bg-submit.webp)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundBlendMode: 'overlay'
          }}
        >
          <h3 className="text-4xl font-bold text-white mb-4">
            Get a Quote for Franchise
          </h3>
          <p className="text-gray-300 mb-8 text-lg max-w-2xl mx-auto">
            Lorem ipsum dolor sit amet consectetur adipiscing elit. Molestie tempor.
          </p>


          {/* Email form */}
          <div className="flex w-[100%] justify-center items-center gap-1 md:gap-0 max-w-xl mx-auto bg-white rounded-full p-1  md:p-1">
            <input
              type="email"
              placeholder="Enter your Email Here"
              className="flex-1 w-[60%] bg-white px-0 pl-6 md:px-6 py-3 rounded-l-full border-none outline-none text-gray-700 text-base"
              style={{
                boxShadow: 'inset 0 4px 12px rgba(0, 0, 0, 0.1)'
              }}
            />
            <button
              className="w-[40%] bg-orange-500 hover:bg-orange-600 text-white mx-auto py-3 rounded-full font-bold text-base transition-colors duration-300"
              style={{
                boxShadow: '0 8px 20px rgba(255, 130, 67, 0.4)'
              }}
            >
              Submit
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default WhyChooseUs