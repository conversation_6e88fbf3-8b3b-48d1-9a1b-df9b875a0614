import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// Common animation configurations
export const fadeUp = {
  y: 30,
  opacity: 0,
  duration: 0.8,
  ease: 'power2.out'
};

export const fadeIn = {
  opacity: 0,
  duration: 0.8,
  ease: 'power2.inOut'
};

export const scaleUp = {
  scale: 0.95,
  opacity: 0,
  duration: 0.8,
  ease: 'back.out(1.2)'
};

export const lineReveal = {
  scaleX: 0,
  duration: 1,
  ease: 'power2.out'
};

// Common scroll trigger configuration
export const scrollTriggerConfig = (trigger, start = 'top 85%') => ({
  scrollTrigger: {
    trigger,
    start,
    toggleActions: 'play none none none',
    // Performance optimizations
    onEnter: () => console.log('ScrollTrigger onEnter:', trigger),
    onLeaveBack: () => console.log('ScrollTrigger onLeaveBack:', trigger),
    // Markers for debugging (remove in production)
    markers: process.env.NODE_ENV === 'development',
    // Performance optimizations
    anticipatePin: 1,
    fastScrollEnd: true
  }
});

// Batch animation for multiple elements
export const staggerChildren = (target, vars) => {
  return gsap.from(target, {
    ...vars,
    stagger: {
      amount: 0.5,
      from: 'random'
    }
  });
};

// Clean up animations when component unmounts
export const setupAnimationCleanup = (ctx) => {
  return () => ctx.revert();
};

// Optimized scroll trigger for gallery items
export const galleryItemAnimation = (target) => {
  return gsap.to(target, {
    y: 0,
    opacity: 1,
    duration: 0.6,
    stagger: 0.1,
    ease: 'power2.out',
    scrollTrigger: {
      trigger: target,
      start: 'top 90%',
      toggleActions: 'play none none none',
      // Performance optimizations
      once: true,
      fastScrollEnd: true
    }
  });
};

// Optimize GSAP for better performance
gsap.ticker.fps(60);
gsap.ticker.lagSmoothing(0);

// Disable GSAP's autoSleep after 2 seconds (keeps animations smooth)
gsap.delayedCall(2, () => {
  gsap.globalTimeline.timeScale(1.2);
});
