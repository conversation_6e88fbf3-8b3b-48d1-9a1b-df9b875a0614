import React, { createContext, useContext } from 'react';

export const images = {
  // Logo
  logo: '/logo.webp',

  // Hero Section
  hero: {
    bg: '/hero/bg-main-hero.webp',
    bg2: '/hero/bg2-main-hero.webp',
    bigLeaf: '/hero/big-leaf.webp',
    comment1: '/hero/comment1.webp',
    comment2: '/hero/comment2.webp',
    fishVegetables: '/hero/fish-vegetables.webp',
    fishVegetables2: '/hero/fish-vegetables2.webp',
    friedChicken: '/hero/fried-chicken.webp',
    grilledChicken: '/hero/grilled-chicken.webp',
    italianPizza: '/hero/italian-pizza.webp',
    leaf2: '/hero/leaf2.webp',
    leaf3: '/hero/leaf3.webp',
    mainHero: '/hero/main-hero.webp',
    mainHeroCopy: '/hero/main-hero copy.webp',
    mainHeroCopy2: '/hero/main-hero copy 2.webp',
    sauce: '/hero/sauce.webp',
  },

  // Carousel
  carousel: {
    bg: '/carousel/bg-carousel.webp',
    bg2: '/carousel/bg2-carousel.webp',
    pizzas: [
      '/carousel/carousel-pizza_1-small.png',
      '/carousel/carousel-pizza_2-small.png',
      '/carousel/carousel-pizza_3-small.png',
      '/carousel/carousel-pizza_4_small.png',
      '/carousel/carousel-pizza_5-small.png',
    ],
  },
    // Footer
    footer: {
      logo: '/gallery/footer/logo 2.svg',
      social: {
        facebook: '/gallery/footer/facebook logo.svg',
        instagram: '/gallery/footer/instagram logo.svg',
        twitter: '/gallery/footer/twitter logo.svg'
      }
    },

  // Gallery
  gallery: {
    backgroundTexture: '/gallery/Rectangle_4629.png',
   
        // Image gallery for header
        imageGallery: {
          headerText: '/gallery/image-gallery/GALLERY.svg',
          images: [
            '/gallery/image-gallery/bt4.png',
            '/gallery/image-gallery/jeswin-thomas-TyV8uETpfOg-unsplash.png'
          ]
        },
        
    feelFood: '/gallery/FeelFood.svg',
    
    freshFastDelicious: {
      svg: '/gallery/Fresh-Fast-Delicious/Fresh-Fast-Delicious.svg',
      rectangle: '/gallery/Fresh-Fast-Delicious/Rectangle 4622.png',
      vector: '/gallery/Fresh-Fast-Delicious/Vector2/Vector2.svg',
    },
    
    lineDesign: {
      line: '/gallery/Line-design/line.svg',
      line2: '/gallery/Line-design/line2/line.svg',
      mainText: '/gallery/Line-design/main-text.svg',
    },
    
    juice: [
      '/gallery/Juice/eiliv-sonas-aceron-NWTPcPE1nJI-unsplash.png',
      '/gallery/Juice/sara-cervera-xB0Kr0D0C8Y-unsplash.png',
    ],
    
    cake: [
      '/gallery/cake/bt2.png',
      '/gallery/cake/micheile-dot-com-MYAGZpr2cik-unsplash.png',
      '/gallery/cake/umesh-soni-LDnmyOaA-ew-unsplash.png',
    ],
    
    chef: [
      '/gallery/chef/benu-marinescu-e6ZOmEfNHLM-unsplash.png',
      '/gallery/chef/c1.png',
      '/gallery/chef/louis-hansel-0sYLBZjgTTw-unsplash.png',
    ],
    
    discount: {
      text: '/gallery/discount/Discount-50%-Weekend.svg',
      rectangle: '/gallery/discount/Rectangle-4625.svg',
    },
    
    endcake: '/gallery/endcake/endcake-image-12.png',
    
    pasta: [
      '/gallery/pasta/pasta-image-12.png',
      '/gallery/pasta/pasta-pinar-kucuk-Ae7jQFDTPk4-unsplash 2.png',
    ],
    
    pizza: '/gallery/pizza/pizza-image-12.png',
    

  },
};

// Create a context for the images
export const ImagesContext = createContext(images);

// Custom hook to use the images
export const useImages = () => useContext(ImagesContext);

// Provider component
export const ImagesProvider = ({ children }) => {
  return (
    <ImagesContext.Provider value={images}>
      {children}
    </ImagesContext.Provider>
  );
};

export default images;
